FROM docker.longbridge-inc.com/long-bridge-algo/builder/algo-python-builder:3.10.14

WORKDIR /python-docker
# 使用 ARG 定义构建时的环境变量
ARG ENV_TYPE
ENV ENV_TYPE ${ENV_TYPE:-dev}
ENV TZ=Asia/Shanghai
RUN echo "ENV_TYPE is $ENV_TYPE"
COPY . .

RUN pip install -r requirements.txt
# python main.py --start_ts 1745562600000 --end_ts 1745564400000
CMD ["python", "main.py", "--callback_server", "--scheduler", "10min", "--checker"]
