"""
这个类用于临时查询 StarRocks 的表数据为 csv 文件导出
"""
import pymysql
import csv
import config
from datetime import datetime

def query_and_export_question_pairs():
    """查询StarRocks的question_pairs表并按一级分类和标准问题分组导出为CSV文件"""
    
    # 数据库连接参数
    connection_params = {
        'host': config.STARROCKS_HOST,
        'port': config.STARROCKS_PORT,
        'user': config.STARROCKS_USER,
        'password': config.STARROCKS_PASSWORD,
        'database': config.STARROCKS_DATABASE,
        'init_command': "SET SESSION time_zone='+08:00'"
    }
    
    conn = None
    cursor = None
    
    try:
        # 连接数据库
        print("正在连接StarRocks数据库...")
        conn = pymysql.connect(**connection_params)
        cursor = conn.cursor()
        print("数据库连接成功")
        
        # 查询总记录数
        cursor.execute("SELECT COUNT(*) FROM `test`.`question_pairs`")
        total_count = cursor.fetchone()[0]
        print(f"总记录数: {total_count}")

        # 查询标准问题数量
        cursor.execute("SELECT COUNT(DISTINCT standard_question) FROM `test`.`question_pairs`")
        standard_q_count = cursor.fetchone()[0]
        print(f"标准问题数量: {standard_q_count}")

        # 查询按一级分类和标准问题分组的数据
        group_query = """
            SELECT 
                catagory_1 as `一级分类`,
                catagory_2 as `二级分类`, 
                catagory_3 as `三级分类`, 
                standard_question as `标准问题`, 
                similar_question as `相似问题`, 
                question_relevant as `问题记录`, 
                standard_answer as `标准答案`, 
                answer_relevant as `答案记录`,
                COUNT(*) OVER (PARTITION BY catagory_1, standard_question) as `标准问题数`
            FROM `test`.`question_pairs` 
            WHERE standard_question NOT IN ('当前会话无访客消息的处理方式是什么？', '客服服务评价如何？')
            ORDER BY catagory_1, standard_question, similar_question
        """
        
        print("正在执行查询...")
        cursor.execute(group_query)
        
        # 获取列名
        column_names = [desc[0] for desc in cursor.description]
        print(f"查询列名: {column_names}")
        
        # 获取所有结果
        all_results = cursor.fetchall()
        total_records = len(all_results)
        print(f"共获取到 {total_records} 条记录")
        
        # 设置每个文件的记录数
        records_per_file = 20000
        
        # 按批次导出数据
        current_batch = []
        file_index = 0
        processed_count = 0

        for i, row in enumerate(all_results):
            # 如果当前批次已满，导出并开始新批次  
            if len(current_batch) >= records_per_file:
                file_index += 1
                export_batch(current_batch, column_names, file_index)
                current_batch = []
            
            current_batch.append(row)
            processed_count += 1
            
            # 显示进度
            if processed_count % 1000 == 0 or processed_count == total_records:
                print(f"\r已处理 {processed_count}/{total_records} 条记录", end="")
        
        # 导出最后一批数据
        if current_batch:
            file_index += 1
            export_batch(current_batch, column_names, file_index)
        
        print(f"\n\n所有数据导出完成！")
        print(f"共导出 {processed_count} 条记录到 {file_index} 个文件")
        
        # 显示一些统计信息
        cursor.execute("""
            SELECT 
                catagory_1 as `一级分类`,
                COUNT(DISTINCT standard_question) as `标准问题种类数`,
                SUM(cnt) as `总记录数`
            FROM (
                SELECT 
                    catagory_1,
                    standard_question,
                    COUNT(*) as cnt
                FROM `test`.`question_pairs` 
                WHERE standard_question NOT IN ('当前会话无访客消息的处理方式是什么？', '客服服务评价如何？')
                GROUP BY catagory_1, standard_question
            ) t
            GROUP BY catagory_1
            ORDER BY catagory_1
        """)
        
        print("\n按一级分类的统计信息:")
        print("一级分类\t标准问题种类数\t总记录数")
        print("-" * 50)
        for row in cursor.fetchall():
            print(f"{row[0]}\t{row[1]}\t{row[2]}")
        
    except Exception as e:
        print(f"导出过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭数据库连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭")

def query_and_export_helpcenter_topics():
    """查询Hive的帮助中心主题数据并导出为CSV文件"""

    # 数据库连接参数 - 使用Hive连接
    connection_params = {
        'host': config.STARROCKS_HOST,  # 假设使用相同的连接配置
        'port': config.STARROCKS_PORT,
        'user': config.STARROCKS_USER,
        'password': config.STARROCKS_PASSWORD,
        'database': 'hive',  # 连接到hive数据库
        'init_command': "SET SESSION time_zone='+08:00'"
    }

    conn = None
    cursor = None

    try:
        # 连接数据库
        print("正在连接数据库...")
        conn = pymysql.connect(**connection_params)
        cursor = conn.cursor()
        print("数据库连接成功")

        # 您提供的SQL查询
        helpcenter_query = """
            SELECT
                topics.id,
                get_json_object(topics.title_locales, '$.zh-CN') AS title_cn,
                get_json_object(topics.title_locales, '$.en') AS title_en,
                get_json_object(topics.title_locales, '$.zh-HK') AS title_hk,
                topics.app_id,
                content_cn.body AS body_cn,
                content_en.body AS body_en,
                content_hk.body AS body_hk
            FROM hive.lb_helpcenter_prod.topics
            LEFT JOIN hive.lb_helpcenter_prod.topic_contents content_cn ON content_cn.topic_id = topics.id AND content_cn.name = 'content'
            LEFT JOIN hive.lb_helpcenter_prod.topic_contents content_en ON content_en.topic_id = topics.id AND content_en.name = 'content_en'
            LEFT JOIN hive.lb_helpcenter_prod.topic_contents content_hk ON content_hk.topic_id = topics.id AND content_hk.name = 'content_hk'
            WHERE topics.app_id IN ('longbridge', 'longbridge_sg')
            ORDER BY topics.id ASC
            LIMIT 100 OFFSET 0
        """

        print("正在执行帮助中心主题查询...")
        cursor.execute(helpcenter_query)

        # 获取列名
        column_names = [desc[0] for desc in cursor.description]
        print(f"查询列名: {column_names}")

        # 获取所有结果
        all_results = cursor.fetchall()
        total_records = len(all_results)
        print(f"共获取到 {total_records} 条记录")

        if total_records == 0:
            print("没有查询到数据，请检查SQL语句和数据库连接")
            return

        # 生成CSV文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"helpcenter_topics_{timestamp}.csv"

        # 导出数据到CSV
        print(f"正在写入CSV文件: {csv_filename}")
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            writer.writerow(column_names)

            # 写入数据行
            for i, row in enumerate(all_results):
                writer.writerow(row)

                # 显示进度
                if (i + 1) % 10 == 0 or (i + 1) == total_records:
                    print(f"\r已写入 {i + 1}/{total_records} 条记录", end="")

        print(f"\n\n帮助中心主题数据导出完成！")
        print(f"文件名: {csv_filename}")
        print(f"共导出 {total_records} 条记录")

        # 显示一些样本数据
        print("\n前3条记录预览:")
        print("-" * 80)
        for i, col_name in enumerate(column_names):
            print(f"{col_name}: {all_results[0][i] if all_results else 'N/A'}")
            if i >= 2:  # 只显示前3列
                break

    except Exception as e:
        print(f"导出过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭数据库连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭")

def export_batch(batch, column_names, file_index):
    """导出一批数据到CSV文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"question_pairs_grouped_batch_{file_index}_{timestamp}.csv"

    print(f"\n正在写入CSV文件: {csv_filename}")
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # 写入表头
        writer.writerow(column_names)

        # 写入数据行
        for row in batch:
            writer.writerow(row)

    print(f"第 {file_index} 批数据导出完成，共 {len(batch)} 条记录")

if __name__ == "__main__":
    # 可以选择运行哪个导出函数
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "helpcenter":
        query_and_export_helpcenter_topics()
    else:
        query_and_export_question_pairs()
