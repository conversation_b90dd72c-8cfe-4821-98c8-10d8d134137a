#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
从帮助中心内容中提取问题
从helpcenter_clean.csv的body中提取问题，每个body最多提取5个问题
"""

import pandas as pd
import requests
import json
import logging
import time
from pathlib import Path

# ========== 配置 ==========
API_KEY = "sk-98JRLqXxymWkcsxerRquWmQ341fAn0aPV6bxODpmr9pf67tX"
API_URL = "https://lboneapi.longbridge-inc.com/v1/chat/completions"
HELPCENTER_FILE = "data/q/helpcenter_clean.csv"  # 帮助中心文件
OUTPUT_FILE = "data/helpcenter_questions.csv"  # 输出文件

# 批量处理配置
BATCH_SIZE = 20  # 每批处理的内容数量
MAX_QUESTIONS_PER_CONTENT = 5  # 每个内容最多提取的问题数量

# ========== 日志配置 ==========
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)

def check_network_connection():
    """检查网络连接"""
    try:
        # 测试API连接
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        test_payload = {
            "model": "gpt-4o-mini",
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 10
        }
        
        resp = requests.post(
            API_URL, 
            headers=headers, 
            data=json.dumps(test_payload), 
            timeout=30,
            verify=True
        )
        
        if resp.status_code == 200:
            logging.info("网络连接正常")
            return True
        else:
            logging.warning(f"API响应异常: {resp.status_code}")
            return False
            
    except requests.exceptions.SSLError as e:
        logging.error(f"SSL连接错误: {e}")
        logging.info("建议检查网络设置或稍后重试")
        return False
    except requests.exceptions.ConnectionError as e:
        logging.error(f"连接错误: {e}")
        logging.info("建议检查网络连接")
        return False
    except Exception as e:
        logging.error(f"网络检查失败: {e}")
        return False

# ========== LLM 调用 ==========
def call_llm(prompt, max_retries=3):
    """调用大模型API，带重试机制"""
    for attempt in range(max_retries):
        try:
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "gpt-4o-mini",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0,
                "max_tokens": 2000
            }
            
            # 增加SSL验证选项和更长的超时时间
            resp = requests.post(
                API_URL, 
                headers=headers, 
                data=json.dumps(payload), 
                timeout=120,  # 增加超时时间
                verify=True,  # 启用SSL验证
                allow_redirects=True
            )
            resp.raise_for_status()
            return resp.json()["choices"][0]["message"]["content"]
            
        except requests.exceptions.Timeout:
            logging.warning(f"LLM调用超时 (尝试 {attempt+1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(5)  # 等待5秒后重试
                continue
            else:
                logging.error("LLM调用超时，已达到最大重试次数")
                return None
                
        except requests.exceptions.SSLError as e:
            logging.warning(f"SSL连接错误 (尝试 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(10)  # SSL错误等待更长时间
                continue
            else:
                logging.error("SSL连接错误，已达到最大重试次数")
                return None
                
        except requests.exceptions.ConnectionError as e:
            logging.warning(f"连接错误 (尝试 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(15)  # 连接错误等待更长时间
                continue
            else:
                logging.error("连接错误，已达到最大重试次数")
                return None
                
        except requests.exceptions.RequestException as e:
            logging.warning(f"网络错误 (尝试 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(8)
                continue
            else:
                logging.error(f"网络错误，已达到最大重试次数: {e}")
                return None
                
        except Exception as e:
            logging.error(f"LLM调用失败: {e}")
            return None
    
    return None

def extract_questions_from_text(text):
    """从文本中提取问题"""
    import re
    
    # 匹配方括号内的内容
    bracket_pattern = r'\[(.*?)\]'
    bracket_matches = re.findall(bracket_pattern, text)
    
    if bracket_matches:
        content = bracket_matches[0]
        items = [item.strip().strip('"\'') for item in content.split(',')]
        questions = [item for item in items if item and len(item.strip()) > 0]
        return questions[:MAX_QUESTIONS_PER_CONTENT]
    
    # 匹配引号内的内容
    quote_pattern = r'"([^"]+)"'
    quote_matches = re.findall(quote_pattern, text)
    questions = [match for match in quote_matches if match and len(match.strip()) > 0]
    return questions[:MAX_QUESTIONS_PER_CONTENT]

def extract_questions_from_content_batch(contents_batch):
    """从一批内容中提取问题"""
    # 构建内容列表
    contents_text = "\n".join([f"{i+1}. {content}" for i, content in enumerate(contents_batch)])
    
    prompt = f"""
请从以下帮助中心内容中提取用户可能问的问题。

帮助中心内容：
{contents_text}

要求：
1. 每个内容提取3-5个用户可能问的问题
2. 问题应该具体、实用，涵盖该内容的主要信息点
3. 问题要自然，符合用户实际提问习惯
4. 避免重复或过于相似的问题
5. 输出格式为JSON对象，key为内容序号，value为问题数组

示例输出格式：
{{
    "1": ["如何开户？", "开户需要准备什么材料？", "开户流程是什么？"],
    "2": ["如何入金？", "支持哪些入金方式？", "入金有手续费吗？"],
    "3": ["如何交易股票？", "交易时间是什么？", "交易手续费是多少？"]
}}

请直接输出JSON对象，不要其他内容。
        """
    
    # 尝试调用LLM，最多重试3次
    for attempt in range(3):
        try:
            result = call_llm(prompt)
            
            if result is None:
                if attempt < 2:  # 还有重试机会
                    logging.warning(f"LLM调用失败，等待后重试 (尝试 {attempt+1}/3)")
                    time.sleep(30)  # 等待30秒后重试
                    continue
                else:
                    logging.error("LLM调用失败，跳过此批次")
                    return {}
            
            # 清理可能的markdown格式
            cleaned_result = result.strip()
            if cleaned_result.startswith('```json'):
                cleaned_result = cleaned_result[7:]
            if cleaned_result.endswith('```'):
                cleaned_result = cleaned_result[:-3]
            cleaned_result = cleaned_result.strip()
            
            try:
                questions_dict = json.loads(cleaned_result)
                if isinstance(questions_dict, dict):
                    return questions_dict
                else:
                    logging.warning(f"返回格式错误: {result[:100]}...")
                    return {}
            except json.JSONDecodeError:
                logging.warning(f"JSON解析失败，尝试手动解析: {result[:100]}...")
                # 手动解析
                questions_dict = {}
                for i in range(len(contents_batch)):
                    questions = extract_questions_from_text(result)
                    if questions:
                        questions_dict[str(i+1)] = questions
                return questions_dict
                
        except Exception as e:
            logging.warning(f"提取问题失败 (尝试 {attempt+1}/3): {e}")
            if attempt < 2:
                time.sleep(30)
                continue
            else:
                logging.error("提取问题失败，跳过此批次")
                return {}
    
    return {}

def load_helpcenter_data(file_path):
    """加载帮助中心数据"""
    try:
        logging.info(f"正在加载帮助中心文件: {file_path}")
        
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        if df.empty:
            logging.error("帮助中心文件为空")
            return None
        
        # 检查必要的列
        required_columns = ['id', 'app_id', 'body']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logging.error(f"缺少必要的列: {missing_columns}")
            logging.info(f"可用的列: {list(df.columns)}")
            return None
        
        logging.info(f"成功加载 {len(df)} 条帮助中心内容")
        
        return df
        
    except Exception as e:
        logging.error(f"加载帮助中心数据失败: {e}")
        return None

def process_content_batches(helpcenter_df):
    """批量处理内容提取问题"""
    all_questions = []
    seen_questions = set()  # 用于去重
    
    # 分批处理内容
    content_batches = [helpcenter_df.iloc[i:i+BATCH_SIZE] 
                      for i in range(0, len(helpcenter_df), BATCH_SIZE)]
    
    total_batches = len(content_batches)
    
    logging.info(f"开始批量处理: {total_batches} 个批次，每批 {BATCH_SIZE} 条内容")
    
    for batch_idx, content_batch in enumerate(content_batches):
        logging.info(f"处理批次 {batch_idx+1}/{total_batches}")
        
        # 提取body内容
        body_contents = content_batch['body'].tolist()
        
        # 提取问题
        questions_dict = extract_questions_from_content_batch(body_contents)
        
        # 处理提取的问题
        for content_idx, content_row in content_batch.iterrows():
            content_num = str(content_idx - content_batch.index[0] + 1)
            
            if content_num in questions_dict:
                questions = questions_dict[content_num]
                
                for question in questions:
                    # 去重检查
                    if question not in seen_questions:
                        seen_questions.add(question)
                        
                        all_questions.append({
                            'question': question,
                            'helpcenter_id': content_row['id'],
                            'app_id': content_row['app_id'],
                            'content': content_row['body']
                        })
                        
                        logging.info(f"提取问题: {question} (ID: {content_row['id']})")
        
        # 添加延迟避免API限制
        time.sleep(1)
    
    return all_questions

def save_questions(questions, output_file):
    """保存提取的问题到CSV"""
    try:
        # 确保输出目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        # 创建DataFrame
        df = pd.DataFrame(questions)
        
        # 保存为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        logging.info(f"提取的问题已保存到: {output_file}")
        logging.info(f"共保存 {len(questions)} 个问题")
        
        return True
        
    except Exception as e:
        logging.error(f"保存问题失败: {e}")
        return False

def analyze_results(questions):
    """分析提取结果"""
    if not questions:
        return
    
    df = pd.DataFrame(questions)
    
    logging.info(f"\n提取结果统计:")
    logging.info(f"总问题数: {len(df)}")
    logging.info(f"涉及帮助中心内容数: {df['helpcenter_id'].nunique()}")
    
    # 应用分布
    app_counts = df['app_id'].value_counts()
    logging.info(f"应用分布:")
    for app, count in app_counts.items():
        logging.info(f"  {app}: {count} 个问题")
    
    # 问题长度统计
    question_lengths = df['question'].str.len()
    logging.info(f"问题长度统计:")
    logging.info(f"  平均长度: {question_lengths.mean():.1f} 字符")
    logging.info(f"  最短长度: {question_lengths.min()} 字符")
    logging.info(f"  最长长度: {question_lengths.max()} 字符")

def display_sample_questions(questions, count=10):
    """显示示例问题"""
    logging.info(f"\n前{count}个问题示例:")
    for i, q in enumerate(questions[:count], 1):
        logging.info(f"  {i:2d}. {q['question']}")
        logging.info(f"      内容ID: {q['helpcenter_id']}, 应用: {q['app_id']}")

def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("帮助中心内容问题提取工具")
    logging.info("=" * 60)
    
    # Step 0: 检查网络连接
    logging.info("Step 0: 检查网络连接...")
    if not check_network_connection():
        logging.error("网络连接异常，程序退出")
        logging.info("请检查网络连接后重试")
        return
    
    # Step 1: 加载帮助中心数据
    logging.info("Step 1: 加载帮助中心数据...")
    helpcenter_df = load_helpcenter_data(HELPCENTER_FILE)
    
    if helpcenter_df is None:
        logging.error("数据加载失败，程序退出")
        return
    
    # Step 2: 批量提取问题
    logging.info("Step 2: 批量提取问题...")
    questions = process_content_batches(helpcenter_df)
    
    if not questions:
        logging.warning("未提取到任何问题，程序退出")
        return
    
    # Step 3: 保存结果
    logging.info("Step 3: 保存结果...")
    saved = save_questions(questions, OUTPUT_FILE)
    
    if saved:
        logging.info("=" * 60)
        logging.info("问题提取完成！")
        logging.info("=" * 60)
        logging.info(f"帮助中心文件: {HELPCENTER_FILE}")
        logging.info(f"输出文件: {OUTPUT_FILE}")
        logging.info(f"提取问题数: {len(questions)}")
        
        # 分析结果
        analyze_results(questions)
        
        # 显示示例
        display_sample_questions(questions)
    else:
        logging.error("❌ 保存文件失败")

if __name__ == "__main__":
    main() 