#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
过滤重复问题工具
对问题数据进行去重处理，基于问题内容的相似度
"""

import pandas as pd
import logging
from difflib import SequenceMatcher
from pathlib import Path

# ========== 配置 ==========
INPUT_FILE = "data/valid_questions_final.csv"  # 输入文件
OUTPUT_FILE = "data/valid_questions_deduplicated.csv"  # 输出文件

# 相似度阈值
SIMILARITY_THRESHOLD = 0.8

# ========== 日志配置 ==========
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)

def calculate_similarity(text1, text2):
    """计算两个文本的相似度"""
    return SequenceMatcher(None, text1, text2).ratio()

def remove_duplicate_questions(df):
    """移除重复问题"""
    logging.info("开始移除重复问题...")
    
    if df.empty:
        logging.warning("输入数据为空")
        return df
    
    # 按app_id分组处理
    result_dfs = []
    
    for app_id, group in df.groupby('app_id'):
        logging.info(f"处理 {app_id} 应用，共 {len(group)} 个问题")
        
        questions = group['question'].tolist()
        indices = group.index.tolist()
        
        # 计算相似度并分组
        similar_groups = []
        processed = set()
        
        for i in range(len(questions)):
            if i in processed:
                continue
                
            current_group = [i]
            processed.add(i)
            
            for j in range(i + 1, len(questions)):
                if j in processed:
                    continue
                    
                similarity = calculate_similarity(questions[i], questions[j])
                
                if similarity >= SIMILARITY_THRESHOLD:
                    current_group.append(j)
                    processed.add(j)
            
            if len(current_group) > 1:
                similar_groups.append(current_group)
                logging.info(f"发现相似问题组: {[questions[idx] for idx in current_group]}")
        
        # 保留每组中最好的问题（通常是第一个）
        filtered_positions = []
        removed_positions = set()
        
        for similar_group in similar_groups:
            best_pos = similar_group[0]  # 保留第一个问题
            for pos in similar_group[1:]:
                removed_positions.add(pos)
        
        for pos in range(len(questions)):
            if pos not in removed_positions:
                filtered_positions.append(pos)
        
        # 保留过滤后的数据
        filtered_group = group.iloc[filtered_positions]
        result_dfs.append(filtered_group)
        
        logging.info(f"{app_id} 应用去重后剩余 {len(filtered_group)} 个问题")
    
    # 合并所有结果
    if result_dfs:
        result_df = pd.concat(result_dfs, ignore_index=True)
        logging.info(f"去重后总共剩余 {len(result_df)} 个问题")
        return result_df
    else:
        logging.warning("没有剩余数据")
        return pd.DataFrame()

def load_data(file_path):
    """加载数据"""
    try:
        logging.info(f"正在加载数据文件: {file_path}")
        
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        if df.empty:
            logging.error("数据文件为空")
            return None
        
        # 检查必要的列
        required_columns = ['question', 'helpcenter_id', 'app_id', 'content']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logging.error(f"缺少必要的列: {missing_columns}")
            logging.info(f"可用的列: {list(df.columns)}")
            return None
        
        logging.info(f"成功加载 {len(df)} 条数据")
        
        return df
        
    except Exception as e:
        logging.error(f"加载数据失败: {e}")
        return None

def save_results(df, output_file):
    """保存结果到CSV"""
    try:
        # 确保输出目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        logging.info(f"结果已保存到: {output_file}")
        logging.info(f"共保存 {len(df)} 条记录")
        
        return True
        
    except Exception as e:
        logging.error(f"保存结果失败: {e}")
        return False

def analyze_results(original_count, filtered_count):
    """分析处理结果"""
    logging.info(f"\n处理结果统计:")
    logging.info(f"原始问题数: {original_count}")
    logging.info(f"去重后问题数: {filtered_count}")
    logging.info(f"去重率: {(original_count - filtered_count)/original_count*100:.1f}%")

def display_sample_results(df, count=5):
    """显示示例结果"""
    logging.info(f"\n前{count}条结果示例:")
    for i, (_, row) in enumerate(df.head(count).iterrows(), 1):
        logging.info(f"  {i}. 问题: {row['question']}")
        logging.info(f"     内容ID: {row['helpcenter_id']}, 应用: {row['app_id']}")
        logging.info(f"     内容: {row['content'][:100]}{'...' if len(row['content']) > 100 else ''}")

def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("重复问题过滤工具")
    logging.info("=" * 60)
    
    # Step 1: 加载数据
    logging.info("Step 1: 加载数据...")
    df = load_data(INPUT_FILE)
    
    if df is None:
        logging.error("加载数据失败，程序退出")
        return
    
    original_count = len(df)
    
    # Step 2: 过滤重复问题
    logging.info("Step 2: 过滤重复问题...")
    filtered_df = remove_duplicate_questions(df)
    
    if filtered_df.empty:
        logging.warning("过滤后没有剩余数据，程序退出")
        return
    
    filtered_count = len(filtered_df)
    
    # Step 3: 保存结果
    logging.info("Step 3: 保存结果...")
    saved = save_results(filtered_df, OUTPUT_FILE)
    
    if saved:
        logging.info("=" * 60)
        logging.info("处理完成！")
        logging.info("=" * 60)
        logging.info(f"输入文件: {INPUT_FILE}")
        logging.info(f"输出文件: {OUTPUT_FILE}")
        
        # 分析结果
        analyze_results(original_count, filtered_count)
        
        # 显示示例
        display_sample_results(filtered_df)
    else:
        logging.error("❌ 保存文件失败")

if __name__ == "__main__":
    main() 