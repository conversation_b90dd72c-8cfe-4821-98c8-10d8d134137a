#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
问题验证和去重工具 (修复版)
验证问题可以被答案回答，并过滤相似问题
"""

import pandas as pd
import requests
import json
import logging
import time
from pathlib import Path
from difflib import SequenceMatcher

# ========== 配置 ==========
API_KEY = "sk-84Plu1xSH6rSHM5tBeFzvhdCsseB3tABPU7V5JQ9xbdI0SzL"
API_URL = "https://lboneapi.longbridge-inc.com/v1/chat/completions"
INPUT_FILE = "data/helpcenter_questions.csv"  # 输入文件
OUTPUT_FILE = "data/helpcenter_questions_filtered.csv"  # 输出文件

# 批量处理配置
BATCH_SIZE = 15  # 每批处理的问题数量
SIMILARITY_THRESHOLD = 0.8  # 相似度阈值

# ========== 日志配置 ==========
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)

def check_network_connection():
    """检查网络连接"""
    try:
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        test_payload = {
            "model": "gpt-4o-mini",
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 10
        }
        
        resp = requests.post(
            API_URL, 
            headers=headers, 
            data=json.dumps(test_payload), 
            timeout=30,
            verify=True
        )
        
        if resp.status_code == 200:
            logging.info("网络连接正常")
            return True
        else:
            logging.warning(f"API响应异常: {resp.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"网络检查失败: {e}")
        return False

def call_llm(prompt, max_retries=3):
    """调用大模型API，带重试机制"""
    for attempt in range(max_retries):
        try:
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "gpt-4o-mini",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0,
                "max_tokens": 1500
            }
            
            resp = requests.post(
                API_URL, 
                headers=headers, 
                data=json.dumps(payload), 
                timeout=120,
                verify=True,
                allow_redirects=True
            )
            resp.raise_for_status()
            return resp.json()["choices"][0]["message"]["content"]
            
        except requests.exceptions.Timeout:
            logging.warning(f"LLM调用超时 (尝试 {attempt+1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(5)
                continue
            else:
                logging.error("LLM调用超时，已达到最大重试次数")
                return None
                
        except requests.exceptions.SSLError as e:
            logging.warning(f"SSL连接错误 (尝试 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(10)
                continue
            else:
                logging.error("SSL连接错误，已达到最大重试次数")
                return None
                
        except requests.exceptions.ConnectionError as e:
            logging.warning(f"连接错误 (尝试 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(15)
                continue
            else:
                logging.error("连接错误，已达到最大重试次数")
                return None
                
        except requests.exceptions.RequestException as e:
            logging.warning(f"网络错误 (尝试 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(8)
                continue
            else:
                logging.error(f"网络错误，已达到最大重试次数: {e}")
                return None
                
        except Exception as e:
            logging.error(f"LLM调用失败: {e}")
            return None
    
    return None

def validate_questions_batch(questions_batch):
    """验证一批问题是否可以被答案回答"""
    # 构建问题-答案对
    qa_pairs = []
    for i, (idx, row) in enumerate(questions_batch.iterrows()):
        qa_pairs.append(f"{i+1}. 问题: {row['question']}\n   答案: {row['content']}")
    
    qa_text = "\n\n".join(qa_pairs)
    
    prompt = f"""
请验证以下问题是否可以被对应的答案回答。

问题-答案对：
{qa_text}

要求：
1. 判断每个问题是否可以被对应的答案内容回答
2. 答案应该包含问题所需的关键信息
3. 如果答案不完整或无法回答，标记为无效
4. 输出JSON格式：{{"序号": {{"valid": true/false, "reason": "原因"}}}}

示例输出：
{{
    "1": {{"valid": true, "reason": "答案包含开户流程的详细信息"}},
    "2": {{"valid": false, "reason": "答案内容过于简单，无法回答具体问题"}},
    "3": {{"valid": true, "reason": "答案详细说明了入金方式和步骤"}}
}}

请直接输出JSON对象，不要其他内容。
        """
    
    # 尝试调用LLM，最多重试3次
    for attempt in range(3):
        try:
            result = call_llm(prompt)
            
            if result is None:
                if attempt < 2:
                    logging.warning(f"LLM调用失败，等待后重试 (尝试 {attempt+1}/3)")
                    time.sleep(30)
                    continue
                else:
                    logging.error("LLM调用失败，跳过此批次")
                    return {}
            
            # 清理可能的markdown格式
            cleaned_result = result.strip()
            if cleaned_result.startswith('```json'):
                cleaned_result = cleaned_result[7:]
            if cleaned_result.endswith('```'):
                cleaned_result = cleaned_result[:-3]
            cleaned_result = cleaned_result.strip()
            
            try:
                validation_dict = json.loads(cleaned_result)
                if isinstance(validation_dict, dict):
                    return validation_dict
                else:
                    logging.warning(f"返回格式错误: {result[:100]}...")
                    return {}
            except json.JSONDecodeError:
                logging.warning(f"JSON解析失败: {result[:100]}...")
                return {}
                
        except Exception as e:
            logging.warning(f"验证问题失败 (尝试 {attempt+1}/3): {e}")
            if attempt < 2:
                time.sleep(30)
                continue
            else:
                logging.error("验证问题失败，跳过此批次")
                return {}
    
    return {}

def calculate_similarity(text1, text2):
    """计算两个文本的相似度"""
    return SequenceMatcher(None, text1, text2).ratio()

def remove_similar_questions(questions_df):
    """移除相似问题"""
    logging.info("开始移除相似问题...")
    
    # 按app_id分组处理
    filtered_questions = []
    
    for app_id in questions_df['app_id'].unique():
        app_questions = questions_df[questions_df['app_id'] == app_id].copy()
        app_questions = app_questions.reset_index(drop=True)
        
        logging.info(f"处理应用 {app_id}，共 {len(app_questions)} 个问题")
        
        # 计算相似度矩阵
        similar_groups = []
        processed = set()
        
        for i in range(len(app_questions)):
            if i in processed:
                continue
                
            current_group = [i]
            processed.add(i)
            
            for j in range(i + 1, len(app_questions)):
                if j in processed:
                    continue
                    
                similarity = calculate_similarity(
                    app_questions.iloc[i]['question'], 
                    app_questions.iloc[j]['question']
                )
                
                if similarity >= SIMILARITY_THRESHOLD:
                    current_group.append(j)
                    processed.add(j)
            
            if len(current_group) > 1:
                similar_groups.append(current_group)
                logging.info(f"发现相似问题组: {[app_questions.iloc[idx]['question'] for idx in current_group]}")
        
        # 保留每组中最好的问题（通常是第一个）
        for group in similar_groups:
            best_idx = group[0]  # 保留第一个问题
            for idx in group[1:]:
                app_questions = app_questions.drop(index=idx)
        
        filtered_questions.append(app_questions)
    
    # 合并所有应用的结果
    if filtered_questions:
        result_df = pd.concat(filtered_questions, ignore_index=True)
        logging.info(f"去重后剩余 {len(result_df)} 个问题")
        return result_df
    else:
        return questions_df

def load_questions_data(file_path):
    """加载问题数据"""
    try:
        logging.info(f"正在加载问题文件: {file_path}")
        
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        if df.empty:
            logging.error("问题文件为空")
            return None
        
        # 检查必要的列
        required_columns = ['question', 'helpcenter_id', 'app_id', 'content']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logging.error(f"缺少必要的列: {missing_columns}")
            logging.info(f"可用的列: {list(df.columns)}")
            return None
        
        logging.info(f"成功加载 {len(df)} 个问题")
        
        return df
        
    except Exception as e:
        logging.error(f"加载问题数据失败: {e}")
        return None

def process_validation_batches(questions_df):
    """批量处理问题验证"""
    valid_questions = []
    
    # 分批处理问题
    question_batches = [questions_df.iloc[i:i+BATCH_SIZE] 
                       for i in range(0, len(questions_df), BATCH_SIZE)]
    
    total_batches = len(question_batches)
    
    logging.info(f"开始批量验证: {total_batches} 个批次，每批 {BATCH_SIZE} 个问题")
    
    for batch_idx, question_batch in enumerate(question_batches):
        logging.info(f"验证批次 {batch_idx+1}/{total_batches}")
        if batch_idx<148:
            continue
        # 验证问题
        validation_results = validate_questions_batch(question_batch)
        
        # 处理验证结果
        for idx, (_, row) in enumerate(question_batch.iterrows()):
            question_num = str(idx + 1)
            
            if question_num in validation_results:
                result = validation_results[question_num]
                if result.get('valid', False):
                    valid_questions.append({
                        'question': row['question'],
                        'helpcenter_id': row['helpcenter_id'],
                        'app_id': row['app_id'],
                        'content': row['content']
                    })
                    logging.info(f"问题有效: {row['question']}")
                else:
                    logging.info(f"问题无效: {row['question']} - {result.get('reason', '未知原因')}")
            else:
                # 如果验证失败，默认保留
                valid_questions.append({
                    'question': row['question'],
                    'helpcenter_id': row['helpcenter_id'],
                    'app_id': row['app_id'],
                    'content': row['content']
                })
                logging.info(f"验证失败，默认保留: {row['question']}")
        
        # 添加延迟避免API限制
        time.sleep(1)
    
    return valid_questions

def save_filtered_questions(questions, output_file):
    """保存过滤后的问题到CSV"""
    try:
        # 确保输出目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        # 创建DataFrame
        df = pd.DataFrame(questions)
        
        # 保存为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        logging.info(f"过滤后的问题已保存到: {output_file}")
        logging.info(f"共保存 {len(questions)} 个问题")
        
        return True
        
    except Exception as e:
        logging.error(f"保存过滤后的问题失败: {e}")
        return False

def analyze_results(original_count, valid_count, final_count):
    """分析处理结果"""
    logging.info(f"\n处理结果统计:")
    logging.info(f"原始问题数: {original_count}")
    logging.info(f"验证有效问题数: {valid_count}")
    logging.info(f"去重后问题数: {final_count}")
    logging.info(f"验证通过率: {valid_count/original_count*100:.1f}%")
    logging.info(f"最终保留率: {final_count/original_count*100:.1f}%")

def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("问题验证和去重工具 (修复版)")
    logging.info("=" * 60)
    
    # Step 0: 检查网络连接
    logging.info("Step 0: 检查网络连接...")
    if not check_network_connection():
        logging.error("网络连接异常，程序退出")
        return
    
    # Step 1: 加载问题数据
    logging.info("Step 1: 加载问题数据...")
    questions_df = load_questions_data(INPUT_FILE)
    
    if questions_df is None:
        logging.error("数据加载失败，程序退出")
        return
    
    original_count = len(questions_df)
    
    # Step 2: 验证问题可以被答案回答
    logging.info("Step 2: 验证问题可以被答案回答...")
    valid_questions = process_validation_batches(questions_df)
    
    if not valid_questions:
        logging.warning("没有找到有效问题，程序退出")
        return
    
    valid_count = len(valid_questions)
    
    # Step 3: 过滤相似问题
    logging.info("Step 3: 过滤相似问题...")
    valid_df = pd.DataFrame(valid_questions)
    filtered_df = remove_similar_questions(valid_df)
    
    final_count = len(filtered_df)
    
    # Step 4: 保存结果
    logging.info("Step 4: 保存结果...")
    saved = save_filtered_questions(filtered_df.to_dict('records'), OUTPUT_FILE)
    
    if saved:
        logging.info("=" * 60)
        logging.info("处理完成！")
        logging.info("=" * 60)
        logging.info(f"输入文件: {INPUT_FILE}")
        logging.info(f"输出文件: {OUTPUT_FILE}")
        
        # 分析结果
        analyze_results(original_count, valid_count, final_count)
        
        # 显示示例
        logging.info(f"\n前5个最终问题示例:")
        for i, (idx, row) in enumerate(filtered_df.head(5).iterrows(), 1):
            logging.info(f"  {i}. {row['question']}")
            logging.info(f"     应用: {row['app_id']}, ID: {row['helpcenter_id']}")
    else:
        logging.error("❌ 保存文件失败")

if __name__ == "__main__":
    main() 