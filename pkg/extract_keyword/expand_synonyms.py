#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
拓展关键词近义词
在现有近义词基础上，增加金融术语和口语化描述
"""

import pandas as pd
import requests
import json
import logging
import time
from pathlib import Path

# ========== 配置 ==========
API_KEY = "sk-98JRLqXxymWkcsxerRquWmQ341fAn0aPV6bxODpmr9pf67tX"
API_URL = "https://lboneapi.longbridge-inc.com/v1/chat/completions"
INPUT_FILE = "data/lb_sg_llm_keywords_with_synonyms.csv"  # 输入文件（包含现有近义词）
OUTPUT_FILE = "data/lb_llm_keywords_expanded.csv"  # 输出文件
MAX_ADDITIONAL_SYNONYMS = 5  # 每个关键词最多增加5个新近义词
BATCH_SIZE = 15  # 批量处理大小

# ========== 日志配置 ==========
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)

# ========== LLM 调用 ==========
def call_llm(prompt):
    try:
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "gpt-4o-mini",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0,
            "max_tokens": 1500
        }
        resp = requests.post(API_URL, headers=headers, data=json.dumps(payload), timeout=30)
        resp.raise_for_status()
        return resp.json()["choices"][0]["message"]["content"]
    except requests.exceptions.Timeout:
        logging.error("LLM调用超时")
        return None
    except requests.exceptions.RequestException as e:
        logging.error(f"LLM调用网络错误: {e}")
        return None
    except Exception as e:
        logging.error(f"LLM调用失败: {e}")
        return None

def extract_synonyms_from_text(text):
    """从文本中提取近义词"""
    import re
    
    # 匹配方括号内的内容
    bracket_pattern = r'\[(.*?)\]'
    bracket_matches = re.findall(bracket_pattern, text)
    
    if bracket_matches:
        # 提取方括号内的内容
        content = bracket_matches[0]
        # 分割并清理
        items = [item.strip().strip('"\'') for item in content.split(',')]
        synonyms = [item for item in items if item and len(item.strip()) > 0]
        return synonyms[:MAX_ADDITIONAL_SYNONYMS]
    
    # 如果还是没找到，尝试匹配引号内的内容
    quote_pattern = r'"([^"]+)"'
    quote_matches = re.findall(quote_pattern, text)
    synonyms = [match for match in quote_matches if match and len(match.strip()) > 0]
    return synonyms[:MAX_ADDITIONAL_SYNONYMS]

def expand_synonyms_for_keywords_batch(keywords_batch):
    """为一批关键词拓展近义词"""
    # 构建批量prompt
    keywords_text = "\n".join([f"{idx+1}. {keyword}" for idx, keyword in enumerate(keywords_batch)])
    
    prompt = f"""
请为以下关键词在现有近义词基础上，进一步拓展金融术语和口语化描述。

关键词列表：
{keywords_text}

要求：
1. 在现有近义词基础上增加新的表达方式
2. 增加金融专业术语（如：证券→券商、证券公司、经纪商、投资银行）
3. 增加口语化描述（如：开户→注册账户、开通账户、申请账户）
4. 增加相关业务词汇（如：交易→买卖、交易操作、投资交易）
5. 每个词都要贴切，不要硬凑
6. 如果没有合适的拓展，可以返回空数组
7. 每个关键词最多返回{MAX_ADDITIONAL_SYNONYMS}个新近义词
8. 输出格式为JSON对象，key为关键词，value为新增近义词数组

示例输出格式：
{{
    "Longbridge": ["长桥集团", "长桥投资", "LB证券"],
    "证券": ["经纪商", "投资银行", "金融机构"],
    "开户": ["注册账户", "开通账户", "申请账户", "账户注册"]
}}

请直接输出JSON对象，不要其他内容。
        """
    
    try:
        result = call_llm(prompt)
        
        if result is None:
            logging.warning(f"为批次关键词拓展近义词失败")
            return {keyword: [] for keyword in keywords_batch}
        
        # 清理可能的markdown格式
        cleaned_result = result.strip()
        if cleaned_result.startswith('```json'):
            cleaned_result = cleaned_result[7:]
        if cleaned_result.endswith('```'):
            cleaned_result = cleaned_result[:-3]
        cleaned_result = cleaned_result.strip()
        
        try:
            synonyms_dict = json.loads(cleaned_result)
            if isinstance(synonyms_dict, dict):
                # 处理每个关键词的新增近义词
                processed_dict = {}
                for keyword in keywords_batch:
                    if keyword in synonyms_dict:
                        synonyms = synonyms_dict[keyword]
                        if isinstance(synonyms, list):
                            # 过滤空值并限制数量
                            valid_synonyms = [s for s in synonyms if s and str(s).strip()]
                            processed_dict[keyword] = valid_synonyms[:MAX_ADDITIONAL_SYNONYMS]
                        else:
                            processed_dict[keyword] = []
                    else:
                        processed_dict[keyword] = []
                return processed_dict
            else:
                logging.warning(f"返回格式错误: {result[:100]}...")
                return {keyword: [] for keyword in keywords_batch}
        except json.JSONDecodeError:
            logging.warning(f"JSON解析失败，尝试手动解析: {result[:100]}...")
            # 手动解析每个关键词
            processed_dict = {}
            for keyword in keywords_batch:
                synonyms = extract_synonyms_from_text(result)
                processed_dict[keyword] = synonyms[:MAX_ADDITIONAL_SYNONYMS]
            return processed_dict
            
    except Exception as e:
        logging.warning(f"为批次关键词拓展近义词失败: {e}")
        return {keyword: [] for keyword in keywords_batch}

def load_existing_synonyms(file_path):
    """加载现有的近义词文件"""
    try:
        logging.info(f"正在加载现有近义词文件: {file_path}")
        
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        if df.empty:
            logging.error("近义词文件为空")
            return []
        
        if 'keyword' not in df.columns or 'synonyms' not in df.columns:
            logging.error("文件中缺少必要的列")
            logging.info(f"可用的列: {list(df.columns)}")
            return []
        
        keywords_data = []
        for _, row in df.iterrows():
            keyword = row['keyword']
            existing_synonyms = row['synonyms']
            
            if pd.notna(keyword) and str(keyword).strip():
                keywords_data.append({
                    'keyword': str(keyword).strip(),
                    'existing_synonyms': str(existing_synonyms).strip() if pd.notna(existing_synonyms) else ""
                })
        
        logging.info(f"成功加载 {len(keywords_data)} 个关键词及其现有近义词")
        return keywords_data
        
    except FileNotFoundError:
        logging.error(f"未找到文件: {file_path}")
        return []
    except Exception as e:
        logging.error(f"加载文件失败: {e}")
        return []

def expand_synonyms_for_all_keywords(keywords_data, batch_size=15):
    """为所有关键词拓展近义词（批量处理）"""
    results = []
    
    logging.info(f"开始为 {len(keywords_data)} 个关键词拓展近义词（批量处理，每批{batch_size}个）...")
    
    # 分批处理
    for i in range(0, len(keywords_data), batch_size):
        batch_data = keywords_data[i:i+batch_size]
        batch_num = i // batch_size + 1
        total_batches = (len(keywords_data) + batch_size - 1) // batch_size
        
        logging.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_data)} 个关键词")
        
        # 提取关键词列表
        batch_keywords = [item['keyword'] for item in batch_data]
        
        # 批量拓展近义词
        batch_new_synonyms = expand_synonyms_for_keywords_batch(batch_keywords)
        
        # 处理结果
        for item in batch_data:
            keyword = item['keyword']
            existing_synonyms = item['existing_synonyms']
            new_synonyms = batch_new_synonyms.get(keyword, [])
            
            # 合并现有近义词和新近义词
            if existing_synonyms:
                all_synonyms = existing_synonyms + "," + ",".join(new_synonyms) if new_synonyms else existing_synonyms
            else:
                all_synonyms = ",".join(new_synonyms) if new_synonyms else ""
            
            results.append({
                'keyword': keyword,
                'synonyms': all_synonyms
            })
            
            logging.info(f"为 '{keyword}' 新增 {len(new_synonyms)} 个近义词: {','.join(new_synonyms) if new_synonyms else '无'}")
        
        # 添加延迟避免API限制
        time.sleep(1)
    
    return results

def save_expanded_results(results, output_file):
    """保存拓展后的结果到CSV"""
    try:
        # 确保输出目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 保存为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        logging.info(f"拓展后的结果已保存到: {output_file}")
        logging.info(f"共处理 {len(results)} 个关键词")
        
        # 统计信息
        total_synonyms = sum(len(row['synonyms'].split(',')) if row['synonyms'] else 0 for row in results)
        avg_synonyms = total_synonyms / len(results) if results else 0
        
        logging.info(f"平均每个关键词 {avg_synonyms:.1f} 个近义词（包含拓展）")
        
        return True
        
    except Exception as e:
        logging.error(f"保存拓展结果失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("关键词近义词拓展工具")
    logging.info("=" * 60)
    
    # Step 1: 加载现有近义词
    logging.info("Step 1: 加载现有近义词...")
    keywords_data = load_existing_synonyms(INPUT_FILE)
    
    if not keywords_data:
        logging.error("没有找到关键词数据，程序退出")
        return
    
    # Step 2: 拓展近义词
    logging.info("Step 2: 拓展近义词...")
    results = expand_synonyms_for_all_keywords(keywords_data, BATCH_SIZE)
    
    if not results:
        logging.error("未能拓展任何近义词，程序退出")
        return
    
    # Step 3: 保存结果
    logging.info("Step 3: 保存结果...")
    saved = save_expanded_results(results, OUTPUT_FILE)
    
    if saved:
        logging.info("=" * 60)
        logging.info("拓展完成！")
        logging.info("=" * 60)
        logging.info(f"输入文件: {INPUT_FILE}")
        logging.info(f"输出文件: {OUTPUT_FILE}")
        logging.info(f"处理关键词数: {len(keywords_data)}")
        logging.info(f"生成结果数: {len(results)}")
        
        # 显示前10个结果示例
        logging.info(f"\n前10个结果示例:")
        for i, result in enumerate(results[:10], 1):
            logging.info(f"  {i:2d}. {result['keyword']} -> {result['synonyms']}")
    else:
        logging.error("❌ 保存文件失败")

if __name__ == "__main__":
    main() 