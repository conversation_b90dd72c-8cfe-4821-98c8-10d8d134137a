import jieba
from collections import Counter, defaultdict
import requests
import json
import logging
import os
import pandas as pd
from pathlib import Path

# ========== 配置 ==========
API_KEY = "sk-98JRLqXxymWkcsxerRquWmQ341fAn0aPV6bxODpmr9pf67tX"
API_URL = "https://lboneapi.longbridge-inc.com/v1/chat/completions"
TOP_N = 5000  # 高频词数量
INPUT_FILE = "../data/longbridge_titles.csv"  # 修改为CSV文件路径
OUTPUT_FILE_FREQ = "../data/lb_keywords.csv"  # 关键词频次CSV

# ========== 日志配置 ==========
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)

# ========== LLM 调用 ==========
def call_llm(prompt):
    try:
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "gpt-4o-mini",  # 如果有其他模型可替换
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0,
            "max_tokens": 1000  # 限制返回的token数量
        }
        resp = requests.post(API_URL, headers=headers, data=json.dumps(payload), timeout=30)
        resp.raise_for_status()
        return resp.json()["choices"][0]["message"]["content"]
    except requests.exceptions.Timeout:
        logging.error("LLM调用超时")
        return None
    except requests.exceptions.RequestException as e:
        logging.error(f"LLM调用网络错误: {e}")
        return None
    except Exception as e:
        logging.error(f"LLM调用失败: {e}")
        return None

def filter_keywords_with_llm(candidate_words):
    logging.info(f"调用LLM过滤 {len(candidate_words)} 个高频词...")
    prompt = f"""
我有以下候选关键词（可能有噪音或同义词）：
{', '.join(candidate_words[:300])}  # 限制前300个词避免token过多

请你：
1. 只删除明显的无意义词（如"发布"、"增长"、"可以"、"应该"、"这个"、"那个"等）。
2. 删除纯数字、单个字符等无意义内容。
3. 删除最基本的停用词（如"的"、"了"、"在"、"是"、"有"、"和"等）。
4. 保留所有有意义的关键词，包括：
   - 产品名、公司名、品牌名
   - 功能词、操作词、业务词
   - 金融术语、投资词汇
   - 活动词、优惠词、营销词
   - 用户相关词汇
5. 尽量保留更多关键词，只要不是明显无意义的词都应该保留。
6. 最终输出一个 JSON 数组，包含筛选后的关键词。

输出格式示例：
["长桥", "证券", "交易", "股票", "投资", "港股", "美股", "开户", "入金", "转仓", "期权", "基金", "專屬", "活動", "優惠", "免佣", "新人", "邀請", "好友", "見面禮", "限時", "特惠", "獎賞", "福利", "促销", "折扣", "免费", "优惠", ...]
    """
    result = call_llm(prompt)
    
    if result is None:
        logging.warning("LLM调用失败，使用原始词列表。")
        return candidate_words  # 返回所有原始词
    
    try:
        filtered_words = json.loads(result)
        logging.info(f"LLM过滤后保留 {len(filtered_words)} 个关键词")
        return filtered_words
    except json.JSONDecodeError:
        logging.warning("LLM返回的内容不是合法JSON，尝试手动解析...")
        # 尝试从文本中提取关键词
        filtered_words = extract_keywords_from_text(result, candidate_words)
        logging.info(f"手动解析后保留 {len(filtered_words)} 个关键词")
        return filtered_words



def extract_keywords_from_text(text, original_words):
    """从文本中提取关键词"""
    import re
    
    # 匹配方括号内的内容
    bracket_pattern = r'\[(.*?)\]'
    bracket_matches = re.findall(bracket_pattern, text)
    
    if bracket_matches:
        # 提取方括号内的内容
        content = bracket_matches[0]
        # 分割并清理
        items = [item.strip().strip('"\'') for item in content.split(',')]
        filtered_items = [item for item in items if item and item in original_words]
        logging.info(f"从方括号中提取到 {len(filtered_items)} 个有效关键词")
        return filtered_items
    
    # 如果还是没找到，尝试匹配引号内的内容
    quote_pattern = r'"([^"]+)"'
    quote_matches = re.findall(quote_pattern, text)
    filtered_matches = [match for match in quote_matches if match in original_words]
    logging.info(f"从引号中提取到 {len(filtered_matches)} 个有效关键词")
    return filtered_matches



# ========== CSV 处理 ==========
def load_csv_title_cn(file_path):
    """加载CSV文件的title_cn列"""
    try:
        logging.info(f"正在加载CSV文件: {file_path}")
        
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        if df.empty:
            logging.error("CSV文件为空")
            return []
        
        # 检查title_cn列是否存在
        if 'title_cn' not in df.columns:
            logging.error("CSV文件中没有'title_cn'列")
            logging.info(f"可用的列: {list(df.columns)}")
            return []
        
        # 提取title_cn列并过滤空值
        titles = []
        for title in df['title_cn']:
            if pd.notna(title) and str(title).strip():
                titles.append(str(title).strip())
        
        logging.info(f"成功加载 {len(titles)} 条title_cn数据")
        
        # 显示前几条数据
        if titles:
            logging.info(f"前5条数据示例:")
            for i, title in enumerate(titles[:5], 1):
                logging.info(f"  {i}. {title}")
        
        return titles
        
    except FileNotFoundError:
        logging.error(f"未找到文件: {file_path}")
        return []
    except Exception as e:
        logging.error(f"加载CSV文件失败: {e}")
        return []

# ========== 核心逻辑 ==========
def get_word_stats(titles):
    word_freq = Counter()
    title_words = []

    for title in titles:
        # 放宽分词条件，保留更多词
        words = [w for w in jieba.cut(title) if len(w.strip()) >= 1 and w.strip()]
        title_words.append(words)
        word_freq.update(words)

    return title_words, word_freq

def get_top_words(word_freq, top_n=1000):
    return [w for w, _ in word_freq.most_common(top_n)]

def build_inverted_index(titles, title_words, valid_keywords):
    inverted = defaultdict(list)
    freq = Counter()

    for idx, words in enumerate(title_words):
        for w in words:
            if w in valid_keywords:
                inverted[w].append(idx)
                freq[w] += 1
    
    logging.info(f"倒排索引构建完成，包含 {len(inverted)} 个关键词")
    return inverted, freq

def analyze_keywords(titles, title_words, filtered_keywords):
    """分析关键词"""
    # 统计每个标题的关键词
    title_keywords = []
    keyword_stats = Counter()
    
    for idx, (title, words) in enumerate(zip(titles, title_words)):
        kws = [w for w in words if w in filtered_keywords]
        title_keywords.append({
            "title_index": idx,
            "title": title,
            "keywords": kws,
            "keyword_count": len(kws)
        })
        keyword_stats.update(kws)
    
    logging.info(f"关键词分析完成，共 {len(keyword_stats)} 个唯一关键词")
    logging.info(f"关键词频次范围: {min(keyword_stats.values()) if keyword_stats else 0} - {max(keyword_stats.values()) if keyword_stats else 0}")
    
    return title_keywords, keyword_stats

def save_frequency_csv(keyword_stats, output_file):
    """保存关键词频次CSV"""
    try:
        # 确保输出目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        # 创建频次数据
        freq_data = []
        for keyword, count in keyword_stats.most_common():
            freq_data.append({
                'keyword': keyword,
                'frequency': count
            })
        
        # 保存为CSV
        df = pd.DataFrame(freq_data)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        logging.info(f"关键词频次已保存到: {output_file}")
        return True
    except Exception as e:
        logging.error(f"保存频次CSV失败: {e}")
        return False



def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("CSV文件关键词提取工具")
    logging.info("=" * 60)
    
    # Step 0: 加载CSV文件的title_cn列
    logging.info("Step 0: 加载CSV文件title_cn列...")
    titles = load_csv_title_cn(INPUT_FILE)
    
    if not titles:
        logging.error("无法加载数据，程序退出")
        return
    
    # Step 1: 分词 & 词频
    logging.info("Step 1: 分词与词频统计...")
    title_words, word_freq = get_word_stats(titles)
    
    logging.info(f"总词数: {len(word_freq)}")
    logging.info(f"高频词示例: {list(word_freq.most_common(10))}")
    
    # Step 2: 高频词
    logging.info("Step 2: 提取高频前1000词...")
    top_words = get_top_words(word_freq, TOP_N)
    logging.info(f"高频词数量: {len(top_words)}")
    
    # Step 3: LLM 过滤
    logging.info("Step 3: 调用LLM过滤关键词...")
    filtered_keywords = filter_keywords_with_llm(top_words)
    logging.info(f"过滤后关键词数量: {len(filtered_keywords)}")
    logging.info(f"过滤后关键词: {filtered_keywords}")
    
    # Step 4: 构建倒排索引
    logging.info("Step 4: 构建倒排索引...")
    inverted_index, final_freq = build_inverted_index(titles, title_words, set(filtered_keywords))
    
    # Step 5: 分析关键词
    logging.info("Step 5: 分析关键词...")
    title_keywords, keyword_stats = analyze_keywords(titles, title_words, filtered_keywords)
    
    # 保存结果
    logging.info("Step 6: 保存结果...")
    
    # 保存关键词频次CSV
    freq_saved = save_frequency_csv(keyword_stats, OUTPUT_FILE_FREQ)
    
    if freq_saved:
        logging.info("=" * 60)
        logging.info("分析完成！")
        logging.info("=" * 60)
        logging.info(f"输入文件: {INPUT_FILE}")
        logging.info(f"总标题数: {len(titles)}")
        logging.info(f"提取关键词数: {len(filtered_keywords)}")
        logging.info(f"有关键词的标题数: {sum(1 for tk in title_keywords if tk['keywords'])}")
        logging.info(f"平均每标题关键词数: {sum(len(tk['keywords']) for tk in title_keywords) / len(title_keywords):.2f}")
        logging.info(f"关键词频次文件: {OUTPUT_FILE_FREQ}")
        
        # 显示前10个最常见关键词
        logging.info(f"\n前10个最常见关键词:")
        for keyword, count in keyword_stats.most_common(10):
            logging.info(f"  - {keyword}: {count} 次")
    else:
        logging.error("❌ 保存文件失败")

if __name__ == "__main__":
    main()
