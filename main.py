import threading

import schedule
import time
import datetime
import os
import shutil
import argparse

import config
from qiyu_api import QiyuAPI
from processor import process_extracted_data
from store_data_to_sr import StarRocksStore
from feishu_bot import FeishuBotNotifier
from task_processor import TaskProcessor

# Helper function to convert timestamp to string
def ts_to_str(timestamp_ms):
    try:
        dt_object = datetime.datetime.fromtimestamp(int(timestamp_ms) / 1000, tz=datetime.timezone(datetime.timedelta(hours=8)))
        return dt_object.strftime("%Y%m%d_%H%M%S")
    except Exception:
        return str(timestamp_ms) # Fallback

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建。"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def cleanup_dir(directory):
    """清空目录内容。"""
    if os.path.exists(directory):
        print(f"清空目录: {directory}")
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                print(f'清理文件或目录 {file_path} 时出错: {e}')

def job(start_ts_arg=None, end_ts_arg=None, interval="30min"):
    """定义要定时执行的任务。可以接受可选的开始和结束时间戳参数。"""
    print(f"\n{'='*20} 开始执行定时任务 @ {datetime.datetime.now()}，间隔: {interval} {'='*20}")

    # 1. 确定时间范围和运行标识符
    start_ts, end_ts = None, None
    run_identifier = None
    content = None

    if start_ts_arg and end_ts_arg:
        start_ts = str(start_ts_arg)
        end_ts = str(end_ts_arg)
        print(f"使用命令行参数指定的时间范围: {start_ts} -> {end_ts}")
    else:
        try:
            beijing_timezone = datetime.timezone(datetime.timedelta(hours=8))
            now_beijing = datetime.datetime.now(beijing_timezone)
            end_ts = str(int(now_beijing.timestamp() * 1000))
            time_delta = None
            if interval == "hourly":
                time_delta = datetime.timedelta(hours=1)
                print("按最近 1 小时导出时间范围 (北京时间)")
            elif interval == "30min":
                time_delta = datetime.timedelta(minutes=30)
                print("按最近 30 分钟导出时间范围 (北京时间)")
            elif interval == "10min":
                time_delta = datetime.timedelta(minutes=10)
                print("按最近 10 分钟导出时间范围 (北京时间)")
            elif interval == "5min":
                time_delta = datetime.timedelta(minutes=5)
                print("按最近 5 分钟导出时间范围 (北京时间)")
            else:
                time_delta = datetime.timedelta(minutes=30) # Default to 30 min
                print(f"未知的间隔 '{interval}'，默认按最近 30 分钟导出时间范围 (北京时间)")

            if time_delta:
                start_ts = str(int((now_beijing - time_delta).timestamp() * 1000))
                print(f"时间范围 (北京时间): {(now_beijing - time_delta).isoformat()} ({start_ts}) -> {now_beijing.isoformat()} ({end_ts})")
            else:
                print("无法确定时间差，任务终止。")
                return
        except Exception as e:
            print(f"计算时间范围时出错: {e}")

    if start_ts and end_ts:
        content = {"start": start_ts, "end": end_ts}
        run_identifier = f"{ts_to_str(start_ts)}_to_{ts_to_str(end_ts)}"
    else:
        print("无法确定时间范围，任务终止。")
        return

    # 2. 创建本次运行的专属目录
    download_run_dir = os.path.join(config.DOWNLOAD_DIR, run_identifier)
    extract_run_dir = os.path.join(config.EXTRACT_DIR, run_identifier)
    ensure_dir(download_run_dir)
    ensure_dir(extract_run_dir)
    print(f"本次运行下载目录: {download_run_dir}")
    print(f"本次运行解压目录: {extract_run_dir}")

    qiyu = QiyuAPI(config.QIYU_APPKEY, config.QIYU_ENCRYPT_KEY)

    # 3. 请求七鱼导出数据 (使用已确定的 content)
    message_id = qiyu.get_qiyu_message(content)
    if not message_id:
        print("请求七鱼导出失败，任务终止。")
        return

    print(f"成功请求七鱼导出，Message ID: {message_id}")

    # 4. 轮询检查导出状态并获取下载链接
    download_url = qiyu.get_qiyu_download_url(message_id)
    if not download_url:
        print("获取下载链接失败，任务终止。")
        return

    print(f"获取到下载链接: {download_url}")

    # 5. 下载文件
    try:
        original_filename = download_url.split('/')[-1].split('?')[0] # 尝试提取文件名
        if not original_filename.lower().endswith('.zip'):
             original_filename = f"qiyu_export_{run_identifier}.zip"
    except:
        original_filename = f"qiyu_export_{run_identifier}.zip"

    # 使用本次运行的专属下载目录
    download_file_path = os.path.join(download_run_dir, original_filename)
    download_success = qiyu.download_file(download_url, download_file_path)
    if not download_success:
        print("下载文件失败，任务终止。")
        return

    # 6. 解压文件到本次运行的专属解压目录
    unzip_success = qiyu.unzip_file(download_file_path, extract_run_dir)
    if not unzip_success:
        print("解压文件失败，任务终止。")
        # 可选择是否在此处删除下载的 zip 文件
        # os.remove(download_file_path)
        return

    # 7. 处理本次运行专属解压目录中的数据
    process_extracted_data(extract_run_dir, run_identifier)

    print(f"\n{'='*20} 定时任务执行完毕 @ {datetime.datetime.now()} {'='*20}")
    
# 新增：启动任务检查器
def run_task_checker(once=True):
    """启动任务检查器，定期检查告警和任务状态
    
    Args:
        once (bool, optional): 如果为True，则只执行一次检查而不是持续运行。默认为True。
    """
    print(f"\n{'='*20} 启动任务检查器 @ {datetime.datetime.now()} {'='*20}")
    
    # 每次执行时，创建新的数据库连接实例
    store = StarRocksStore(
        host=config.STARROCKS_HOST,
        port=config.STARROCKS_PORT,
        user=config.STARROCKS_USER,
        password=config.STARROCKS_PASSWORD,
        database=config.STARROCKS_DATABASE
    )
    
    # 连接数据库
    if not store.connect():
        print("[ERROR] 连接 StarRocks 数据库失败，任务检查器无法启动")
        return False  # 返回False表示执行失败
        
    notifier = FeishuBotNotifier()
    
    # 创建任务处理器
    processor = TaskProcessor(store, notifier)
    
    # 启动检查器
    try:
        if once:
            # 只执行一次检查
            processor.run_once()
        else:
            # 持续检查（已废弃）
            processor.run_checker()
        return True  # 返回True表示执行成功
    except KeyboardInterrupt:
        print("[INFO] 收到中断信号，任务检查器停止")
    except Exception as e:
        print(f"[ERROR] 运行任务检查器时出错: {e}")
    finally:
        # 函数结束前确保断开数据库连接，避免连接泄漏
        # TaskProcessor.run_once 方法已经负责关闭连接，这里不需要再次关闭
        pass
    
    return False  # 如果发生异常，返回False


# 启动飞书回调服务的函数
def run_feishu_callback_server():
    """启动飞书回调服务的函数"""
    try:
        # 动态导入，避免循环引用
        from feishu_callback_server import app
        # 使用线程安全的服务器
        app.run(host='0.0.0.0', port=8080, threaded=True)
    except Exception as e:
        print(f"启动飞书回调服务失败: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从七鱼拉取会话数据并处理。")
    parser.add_argument("--start_ts", type=int, help="开始时间的毫秒时间戳 (可选)", default=None)
    parser.add_argument("--end_ts", type=int, help="结束时间的毫秒时间戳 (可选)", default=None)
    parser.add_argument("--callback_server", action="store_true", help="启动飞书回调服务器", default=False)
    parser.add_argument("--scheduler", type=str, default="30min", choices=["off", "hourly", "30min", "10min", "5min"], help="设置定时任务调度器间隔: off, hourly, 30min, 10min, 5min (default: 30min)")
    # 新增：任务检查器参数
    parser.add_argument("--checker", action="store_true", help="启动任务检查器（默认立即执行一次，并设置每日10点定时执行）", default=False)
    args = parser.parse_args()

    print("启动 VoxInspect 服务...")

    # 启动飞书回调服务
    if args.callback_server:
        print("正在启动飞书回调服务...")
        callback_thread = threading.Thread(target=run_feishu_callback_server)
        callback_thread.daemon = True  # 设置为守护线程，这样主程序退出时，线程也会退出
        callback_thread.start()
        print("飞书回调服务已启动，监听端口: 8080")
        
    # 新增：启动任务检查器（每日10点定时执行）
    if args.checker:
        print("正在设置任务检查器定时任务...")
        # 设置每天定时执行一次任务检查
        schedule.every().day.at(config.CHECKER_DEFAULT_TIME).do(run_task_checker, once=True)
        print(f"任务检查器已设置为每天 {config.CHECKER_DEFAULT_TIME} 执行一次")
        
        # 如果只启用了checker且没有启用其他任务，则需要一个循环来运行定时任务
        if not args.scheduler and args.start_ts is None and args.end_ts is None:
            try:
                print("按 Ctrl+C 中断运行...")
                # 立即执行一次任务检查
                print("立即执行一次任务检查...")
                run_task_checker(once=True)
                
                # 进入定时任务循环，不会自动退出
                print(f"已设置每天 {config.CHECKER_DEFAULT_TIME} 执行检查任务，等待下次执行...")
                while True:
                    schedule.run_pending()
                    time.sleep(1)
            except KeyboardInterrupt:
                print("收到中断信号，程序退出")
                exit(0)

    # 如果指定了时间范围，直接使用指定的时间范围执行任务
    if args.start_ts is not None and args.end_ts is not None:
        print(f"使用指定的时间范围执行任务: {args.start_ts} -> {args.end_ts}")
        job(start_ts_arg=args.start_ts, end_ts_arg=args.end_ts, interval="off")
        if args.scheduler == "off":
            print("任务执行完毕，程序退出。")
            exit(0)
    else:
        # Determine the initial interval
        initial_interval = args.scheduler if args.scheduler != "off" else "30min"

        # 立即执行一次任务，获取最近 interval 的数据
        print(f"立即执行一次任务，获取最近 {initial_interval} 的数据...")
        beijing_timezone = datetime.timezone(datetime.timedelta(hours=8))
        now_beijing = datetime.datetime.now(beijing_timezone)
        end_ts_initial = int(now_beijing.timestamp() * 1000)
        time_delta_initial = None
        if initial_interval == "hourly":
            time_delta_initial = datetime.timedelta(hours=1)
        elif initial_interval == "30min":
            time_delta_initial = datetime.timedelta(minutes=30)
        elif initial_interval == "10min":
            time_delta_initial = datetime.timedelta(minutes=10)
        elif initial_interval == "5min":
            time_delta_initial = datetime.timedelta(minutes=5)
        else:
            time_delta_initial = datetime.timedelta(minutes=30) # Default

        if time_delta_initial:
            start_ts_initial = int((now_beijing - time_delta_initial).timestamp() * 1000)
            print(f"初始执行时间范围 (北京时间): {(now_beijing - time_delta_initial).isoformat()} ({start_ts_initial}) -> {now_beijing.isoformat()} ({end_ts_initial})")
            job(start_ts_arg=start_ts_initial, end_ts_arg=end_ts_initial, interval=initial_interval)
        else:
            print("无法确定初始执行的时间差。")

    # 设置定时任务调度器
    if args.scheduler != "off":
        print("正在设置定时任务调度器...")
        # 保留已有的检查器定时任务，仅清除其他定时任务
        if not args.checker:
            schedule.clear() # 清除所有已有的定时任务
            
        if args.scheduler == "hourly":
            schedule.every().hour.at(":00").do(job, interval="hourly")
            print("定时任务已配置为每小时的 00 分执行任务，数据窗口为最近 1 小时。")
        elif args.scheduler == "30min":
            schedule.every().hour.at(":00").do(job, interval="30min")
            schedule.every().hour.at(":30").do(job, interval="30min")
            print("定时任务已配置为每小时的 00 分和 30 分执行任务，数据窗口为最近 30 分钟。")
        elif args.scheduler == "10min":
            # 在每小时的10分、20分、30分、40分、50分和0分执行
            schedule.every().hour.at(":10").do(job, interval="10min")
            schedule.every().hour.at(":20").do(job, interval="10min")
            schedule.every().hour.at(":30").do(job, interval="10min")
            schedule.every().hour.at(":40").do(job, interval="10min")
            schedule.every().hour.at(":50").do(job, interval="10min")
            schedule.every().hour.at(":00").do(job, interval="10min")
            print("定时任务已配置为每小时的10分、20分、30分、40分、50分和0分执行任务，数据窗口为最近10分钟。")
        elif args.scheduler == "5min":
            # 在每小时的5分、10分、15分、...、55分和0分执行
            schedule.every().hour.at(":05").do(job, interval="5min")
            schedule.every().hour.at(":10").do(job, interval="5min")
            schedule.every().hour.at(":15").do(job, interval="5min")
            schedule.every().hour.at(":20").do(job, interval="5min")
            schedule.every().hour.at(":25").do(job, interval="5min")
            schedule.every().hour.at(":30").do(job, interval="5min")
            schedule.every().hour.at(":35").do(job, interval="5min")
            schedule.every().hour.at(":40").do(job, interval="5min")
            schedule.every().hour.at(":45").do(job, interval="5min")
            schedule.every().hour.at(":50").do(job, interval="5min")
            schedule.every().hour.at(":55").do(job, interval="5min")
            schedule.every().hour.at(":00").do(job, interval="5min")
            print("定时任务已配置为每5分钟执行一次任务，数据窗口为最近5分钟。")

        while True:
            schedule.run_pending()
            time.sleep(1)
    else:
        print("已禁用定时任务调度器，仅执行一次任务后退出。")