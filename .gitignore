# Python字节码文件
__pycache__/
*.py[cod]
*$py.class

# C扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# 安装程序日志
pip-log.txt
pip-delete-this-directory.txt

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# 环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/

# IDE设置
.idea/
.vscode/
*.swp
*.swo

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 日志文件
*.log

# 数据库文件
*.db
*.sqlite3

# 其他
*.bak
*.tmp
*.temp

# 项目特定目录
# 下载的数据文件
downloads/
# 解压后的数据文件
extracted_data/
# 分析结果
analysis_results/

qa_results/
