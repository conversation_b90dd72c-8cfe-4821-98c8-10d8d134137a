#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查询二级聚类结果
"""

import json
import pandas as pd
from datetime import datetime
from typing import Optional, List

try:
    import lancedb
    LANCEDB_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 lancedb: pip install lancedb")
    LANCEDB_AVAILABLE = False


class SecondaryClusterQuery:
    """二级聚类结果查询器"""
    
    def __init__(self):
        if LANCEDB_AVAILABLE:
            self.db = lancedb.connect("./user_demand_clustering_db")
        else:
            self.db = None
            print("[ERROR] LanceDB 不可用")
    
    def list_tables(self):
        """列出所有表"""
        if not self.db:
            return []
        return self.db.table_names()
    
    def query_secondary_clusters(self, table_name: str = "secondary_clusters") -> pd.DataFrame:
        """查询二级聚类结果
        
        Args:
            table_name: 表名
            
        Returns:
            二级聚类结果DataFrame
        """
        if not self.db:
            print("[ERROR] LanceDB 不可用")
            return pd.DataFrame()
        
        try:
            table = self.db.open_table(table_name)
            df = table.to_pandas()
            
            # 按总频次排序
            df = df.sort_values('total_frequency', ascending=False)
            
            return df
            
        except Exception as e:
            print(f"[ERROR] 查询失败: {e}")
            return pd.DataFrame()
    
    def search_themes(self, query: str, table_name: str = "secondary_clusters") -> pd.DataFrame:
        """搜索包含特定关键词的主题
        
        Args:
            query: 搜索关键词
            table_name: 表名
            
        Returns:
            搜索结果DataFrame
        """
        df = self.query_secondary_clusters(table_name)
        
        if df.empty:
            return df
        
        # 在主题名称和主要诉求中搜索
        mask = (
            df['theme_name'].str.contains(query, case=False, na=False) |
            df['main_representative_demand'].str.contains(query, case=False, na=False)
        )
        
        return df[mask]
    
    def get_theme_details(self, cluster_id: int, 
                         table_name: str = "secondary_clusters") -> dict:
        """获取特定主题的详细信息
        
        Args:
            cluster_id: 二级聚类ID
            table_name: 表名
            
        Returns:
            主题详细信息
        """
        df = self.query_secondary_clusters(table_name)
        
        theme_row = df[df['secondary_cluster_id'] == cluster_id]
        
        if theme_row.empty:
            return {}
        
        return theme_row.iloc[0].to_dict()
    
    def print_summary(self, table_name: str = "secondary_clusters"):
        """打印二级聚类结果摘要"""
        df = self.query_secondary_clusters(table_name)
        
        if df.empty:
            print("没有找到二级聚类数据")
            return
        
        print(f"用户诉求二级聚类结果摘要 (表: {table_name})")
        print("=" * 70)
        print(f"主题数量: {len(df)}")
        print(f"总子聚类数: {df['sub_clusters_count'].sum()}")
        print(f"总诉求数: {df['total_demands'].sum()}")
        print(f"总频次: {df['total_frequency'].sum()}")
        print(f"平均主题大小: {df['sub_clusters_count'].mean():.1f} 个子聚类")
        
        print(f"\n用户诉求主题分布:")
        print("-" * 70)
        
        for i, (_, row) in enumerate(df.head(10).iterrows(), 1):
            print(f"{i:2d}. 【{row['theme_name']}】")
            print(f"    {row['main_representative_demand']}")
            print(f"    {row['sub_clusters_count']}个子类 | {row['total_demands']}个诉求 | 频次:{row['total_frequency']}")
            if row['keywords']:
                print(f"    关键词: {', '.join(row['keywords'])}")
            print()
    
    def print_theme_details(self, cluster_id: int, table_name: str = "secondary_clusters"):
        """打印特定主题的详细信息"""
        details = self.get_theme_details(cluster_id, table_name)
        
        if not details:
            print(f"未找到主题 {cluster_id}")
            return
        
        print(f"主题详情: 【{details['theme_name']}】")
        print("=" * 60)
        print(f"主要诉求: {details['main_representative_demand']}")
        print(f"子聚类数量: {details['sub_clusters_count']}")
        print(f"总诉求数: {details['total_demands']}")
        print(f"总频次: {details['total_frequency']}")
        print(f"平均频次: {details['avg_frequency']:.1f}")
        print(f"平均相似度: {details['avg_similarity']:.3f}")
        
        if details['keywords']:
            print(f"关键词: {', '.join(details['keywords'])}")
        
        print(f"\n包含的子聚类:")
        print("-" * 60)
        
        sub_clusters = details['sub_cluster_details']
        for i, sub_cluster in enumerate(sub_clusters, 1):
            print(f"{i:2d}. {sub_cluster['representative_demand']}")
            print(f"    诉求数: {sub_cluster['cluster_size']}, 频次: {sub_cluster['total_frequency']}, 相似度: {sub_cluster['avg_similarity']:.3f}")


def main():
    """主函数"""
    query_tool = SecondaryClusterQuery()
    
    # 列出可用表
    tables = query_tool.list_tables()
    print(f"可用表: {tables}")
    
    if "secondary_clusters" not in tables:
        print("没有找到二级聚类结果表，请先运行二级聚类分析:")
        print("python secondary_clustering.py")
        return
    
    # 打印摘要
    query_tool.print_summary()
    
    # 交互式查询示例
    print(f"\n" + "=" * 70)
    print("交互式查询示例:")
    print("=" * 70)
    
    # 搜索示例
    search_keywords = ["开户", "客服", "账户", "资金"]
    for keyword in search_keywords:
        results = query_tool.search_themes(keyword)
        if not results.empty:
            print(f"\n🔍 搜索主题 '{keyword}' 的结果:")
            for _, row in results.head(2).iterrows():
                print(f"  - 【{row['theme_name']}】{row['main_representative_demand']} ({row['sub_clusters_count']}个子类)")
    
    # 显示第一个主题的详细信息
    if not query_tool.query_secondary_clusters().empty:
        print(f"\n" + "=" * 70)
        print("示例：第一个主题的详细信息")
        print("=" * 70)
        first_cluster_id = query_tool.query_secondary_clusters().iloc[0]['secondary_cluster_id']
        query_tool.print_theme_details(first_cluster_id)


if __name__ == "__main__":
    main() 