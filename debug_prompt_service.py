#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Prompt Service 调试脚本
用于调试和测试 Prompt Service API 的功能
"""

import sys
import json
import argparse
import time
from datetime import datetime

# 导入现有模块
import config
from qiyu_session_service import get_session_messages, format_session_messages
from prompt_service import PromptServiceAPI


class PromptServiceDebugger:
    """Prompt Service 调试器"""
    
    def __init__(self):
        """初始化调试器"""
        self.prompt_service = PromptServiceAPI(config.PROMPT_SERVICE_API_URL)
        print(f"[INFO] 使用 Prompt Service ID: {config.PROMPT_SERVICE_ID}")
        print(f"[INFO] API URL: {config.PROMPT_SERVICE_API_URL}")
    
    def debug_session(self, session_id, verbose=False):
        """
        调试指定会话ID的 Prompt Service 处理过程
        
        Args:
            session_id (str): 会话ID
            verbose (bool): 是否显示详细信息
            
        Returns:
            dict: 调试结果
        """
        print(f"\n{'='*60}")
        print(f"开始调试会话: {session_id}")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        # 第一步: 获取会话消息
        print("\n[步骤 1] 获取七鱼会话消息...")
        session_data = get_session_messages(session_id)
        
        if not session_data or session_data.get('code') != 200:
            error_msg = session_data.get('message', '未知错误') if session_data else '无响应数据'
            print(f"[ERROR] 获取会话消息失败: {error_msg}")
            return {"success": False, "error": f"获取会话消息失败: {error_msg}"}
        
        # 格式化消息
        formatted_messages = format_session_messages(session_data)
        print(f"[INFO] 成功获取 {len(formatted_messages)} 条消息")
        
        if verbose:
            print("\n--- 会话消息详情 ---")
            for i, msg in enumerate(formatted_messages[:100]):  # 只显示前10条
                print(f"{i+1:2d}. {msg}")
            if len(formatted_messages) > 100:
                print(f"... 还有 {len(formatted_messages) - 100} 条消息")
        
        # 第二步: 构建 Prompt Service 请求
        print("\n[步骤 2] 构建 Prompt Service 请求...")
        
        # 构建对话内容
        conversation_text = "\n".join(formatted_messages)
        
        # 构建请求载荷（按照 processor.py 中的格式）
        payload = {
            "id": config.PROMPT_SERVICE_ID,
            "Q": conversation_text,
            "ref": "",
            "language": "zh-CN",
            "grpc_timeout": config.GRPC_TIME
        }
        
        if verbose:
            print(f"[DEBUG] 请求载荷:")
            print(json.dumps(payload, ensure_ascii=False, indent=2))
        
        # 第三步: 发送请求到 Prompt Service
        print("\n[步骤 3] 发送请求到 Prompt Service...")
        start_time = time.time()
        
        response = self.prompt_service.send_prompt(payload)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if not response:
            print("[ERROR] Prompt Service 请求失败")
            return {
                "success": False, 
                "error": "Prompt Service 请求失败",
                "processing_time": processing_time
            }
        
        print(f"[INFO] Prompt Service 请求成功 (耗时: {processing_time:.2f}秒)")
        
        # 第四步: 解析和显示响应
        print("\n[步骤 4] 解析 Prompt Service 响应...")
        
        # 首先显示响应的基本结构信息
        print(f"[DEBUG] 响应类型: {type(response)}")
        print(f"[DEBUG] 响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        if verbose:
            print(f"[DEBUG] 完整原始响应:")
            print(json.dumps(response, ensure_ascii=False, indent=2))
        
        # 提取 AI 响应
        ai_response = response.get('ai_response')
        if not ai_response:
            print("[ERROR] 响应中缺少 'ai_response' 字段")
            print(f"[DEBUG] 可用的响应字段: {list(response.keys()) if isinstance(response, dict) else 'N/A'}")
            
            # 尝试其他可能的字段名作为 AI 响应
            possible_ai_fields = ['result', 'data', 'response', 'output', 'content', 'ai_result', 'answer']
            found_alternative = False
            
            for field in possible_ai_fields:
                if field in response and response[field]:
                    print(f"[INFO] 尝试使用 '{field}' 作为 AI 响应")
                    print(f"[DEBUG] {field} 内容类型: {type(response[field])}")
                    if isinstance(response[field], (str, dict)):
                        print(f"[DEBUG] {field} 内容预览: {str(response[field])[:200]}...")
                        # 使用这个字段作为 ai_response
                        ai_response = response[field]
                        found_alternative = True
                        break
            
            if not found_alternative:
                return {
                    "success": False,
                    "error": "响应中缺少 'ai_response' 字段且无法找到替代字段",
                    "raw_response": response,
                    "processing_time": processing_time
                }
        
        # 尝试解析结果字符串
        result_str = self._extract_result_string(ai_response)
        
        if not result_str:
            print("[ERROR] 无法从响应中提取结果字符串")
            print(f"[DEBUG] ai_response 结构: {list(ai_response.keys()) if isinstance(ai_response, dict) else 'Not a dict'}")
            return {
                "success": False,
                "error": "无法从响应中提取结果字符串", 
                "ai_response": ai_response,
                "processing_time": processing_time
            }
        
        print(f"[INFO] 成功提取结果字符串 (长度: {len(result_str)} 字符)")
        
        # 尝试解析 JSON 结果
        try:
            # 清理可能的 markdown 代码块标记
            cleaned_result_str = result_str.strip().strip('```json').strip('```').strip()
            analysis_data = json.loads(cleaned_result_str)
            
            print("[INFO] JSON 解析成功")
            
            # 显示分析结果摘要
            self._display_analysis_summary(analysis_data)
            
            if verbose:
                print("\n--- 完整分析结果 ---")
                print(json.dumps(analysis_data, ensure_ascii=False, indent=2))
            
            return {
                "success": True,
                "session_id": session_id,
                "message_count": len(formatted_messages),
                "analysis_data": analysis_data,
                "processing_time": processing_time,
                "raw_response": response
            }
            
        except json.JSONDecodeError as e:
            print(f"[ERROR] JSON 解析失败: {e}")
            print(f"原始 result 字符串 (前500字符): {result_str[:500]}...")
            
            return {
                "success": False,
                "error": f"JSON 解析失败: {str(e)}",
                "raw_result": result_str,
                "processing_time": processing_time
            }
    
    def _extract_result_string(self, ai_response):
        """
        从不同格式的 AI 响应中提取结果字符串
        
        Args:
            ai_response: AI 响应数据
            
        Returns:
            str: 提取的结果字符串，如果找不到则返回 None
        """
        if not ai_response:
            return None
            
        # 尝试不同的字段名
        possible_result_fields = ['result', 'content', 'response', 'output', 'text', 'data']
        
        for field in possible_result_fields:
            if isinstance(ai_response, dict) and field in ai_response:
                result = ai_response[field]
                if isinstance(result, str) and result.strip():
                    print(f"[DEBUG] 从字段 '{field}' 中提取结果")
                    return result
                elif isinstance(result, dict):
                    # 如果结果是字典，尝试从字典中提取字符串
                    for sub_field in possible_result_fields:
                        if sub_field in result and isinstance(result[sub_field], str):
                            print(f"[DEBUG] 从嵌套字段 '{field}.{sub_field}' 中提取结果")
                            return result[sub_field]
        
        # 如果 ai_response 本身就是字符串
        if isinstance(ai_response, str):
            print("[DEBUG] ai_response 本身是字符串")
            return ai_response
            
        print(f"[DEBUG] 无法从 ai_response 中提取结果字符串")
        print(f"[DEBUG] ai_response 类型: {type(ai_response)}")
        if isinstance(ai_response, dict):
            print(f"[DEBUG] ai_response 键: {list(ai_response.keys())}")
            
        return None

    def _display_analysis_summary(self, analysis_data):
        """显示分析结果摘要"""
        print("\n--- 分析结果摘要 ---")
        
        # 用户诉求
        user_demand = analysis_data.get('用户诉求', {})
        if user_demand:
            print(f"用户核心诉求: {user_demand.get('用户核心诉求', '未识别')}")
        
        # 用户情绪
        emotion = analysis_data.get('用户情绪', {})
        if emotion:
            print(f"用户情绪: {emotion.get('情绪类型', '未知')} ({emotion.get('强度判断', '未知')})")
        
        # 关键词匹配
        keywords = analysis_data.get('关键词匹配', {})
        if keywords:
            match_result = keywords.get('匹配结论', False)
            print(f"关键词匹配: {'是' if match_result else '否'}")
            if match_result and keywords.get('具体词汇及频次'):
                matched_words = list(keywords.get('具体词汇及频次', {}).keys())[:3]
                print(f"主要匹配词汇: {', '.join(matched_words)}")
        
        # 风险识别
        risk = analysis_data.get('风险点识别', {})
        if risk:
            print(f"投诉风险: {risk.get('投诉风险', '未知')}")
        
        # 解决结果
        solution = analysis_data.get('解决结果', {})
        if solution:
            print(f"处理结果: {solution.get('客服处理结果', '未知')}")
        
        # 规则判断
        rule_judgment = analysis_data.get('规则判断', {})
        if rule_judgment:
            print(f"规则命中: {rule_judgment.get('规则命中', '无')}")
            print(f"告警等级: {rule_judgment.get('规则命中等级', '无')}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Prompt Service 调试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python debug_prompt_service.py 12345678
  python debug_prompt_service.py 12345678 --verbose
  python debug_prompt_service.py 12345678 -v
        """
    )
    
    parser.add_argument(
        'session_id', 
        help='要调试的会话ID'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细信息，包括完整的会话消息和分析结果'
    )
    
    args = parser.parse_args()
    
    # 创建调试器
    debugger = PromptServiceDebugger()
    
    # 执行调试
    result = debugger.debug_session(args.session_id, args.verbose)
    
    # 显示最终结果
    print(f"\n{'='*60}")
    if result["success"]:
        print("调试完成 ✓")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        if not args.verbose:
            print("使用 --verbose 参数查看详细信息")
    else:
        print("调试失败 ✗")
        print(f"错误信息: {result.get('error', '未知错误')}")
    print(f"{'='*60}")
    
    # 返回状态码
    sys.exit(0 if result["success"] else 1)


if __name__ == '__main__':
    main() 