import json
import os
import datetime
import config # 导入 config
from feishu_bot import FeishuBotNotifier # 导入飞书机器人
from store_data_to_sr import StarRocksStore # 导入 StarRocks 存储类

class ResponseProcessor:
    """处理来自 Prompt Service 的响应，解析、存储并根据规则触发告警。"""

    # 定义用于告警规则的情绪和强度集合 满意、中立、焦虑、愤怒、失望、困惑、急切、沮丧、怀疑
    # NEGATIVE_EMOTIONS = {"焦虑", "不满", "愤怒", "失望", "困惑", "急切", "沮丧", "怀疑"}
    # HIGH_STRENGTHS = {"重度"}
    # 告警等级 map 高强度负面情绪->一级 命中关键词+中度负面情绪+中度投诉风险->二级 命中关键词+中度负面情绪->三级 命中关键词+中度投诉风险->三级
    ALERT_RULES = {
        "高强度负面情绪" : "一级",
        "命中关键词+中度负面情绪+中度投诉风险" : "二级",
        "命中关键词+中度负面情绪" : "三级",
        "命中关键词+中度投诉风险" : "三级"
    }

    def __init__(self, base_output_dir, run_identifier, feishu_notifier: FeishuBotNotifier):
        """
        初始化 ResponseProcessor。

        Args:
            base_output_dir (str): 用于存储处理后响应的基础目录路径。
            run_identifier (str): 本次运行的标识符，用于创建子目录。
            feishu_notifier (FeishuBotNotifier): 用于发送飞书告警的实例。
        """
        self.base_output_dir = base_output_dir
        self.run_identifier = run_identifier
        self.output_dir = os.path.join(self.base_output_dir, self.run_identifier) # 计算本次运行的完整输出目录
        self.feishu_notifier = feishu_notifier
        # self.alert_keywords = set(config.ALERT_KEYWORDS) # 不再使用关键词列表进行告警

        # 确保本次运行的输出子目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"分析结果将存储在: {self.output_dir}")

        # 检查是否配置飞书自建应用的 appkey 和 secret
        if not config.FEISHU_APP_ID or not config.FEISHU_APP_SECRET:
            print("[WARN] 未配置飞书自建应用的 appkey 或 secret，告警功能禁用。")
            self.feishu_notifier = None
        else:
            print("[INFO] 已配置飞书自建应用的 appkey 和 secret，告警功能启用。")
             
        # 初始化 StarRocks 存储
        self.starrocks_store = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE
        )
        
        # 连接数据库
        if self.starrocks_store.connect():
            print("[INFO] StarRocks 数据库连接成功")
        else:
            print("[WARN] StarRocks 数据库连接失败，数据将不会存储到数据库")


    def process_response(self, session_id, response_data, staff_name="未知", customer_name="未知", member_id=0):
        """
        处理单个会话的 Prompt Service 响应。

        Args:
            session_id (str): 对应的会话 ID。
            response_data (dict): 从 Prompt Service API 返回的原始响应字典。
            staff_name (str): 客服名称，默认为"未知"。
            customer_name (str): 客户名称，默认为"未知"。
            member_id (int): 会员ID，默认为0。

        Returns:
            bool: 处理是否成功。
        """
        if not response_data:
            print(f"[WARN] Session {session_id}: 收到的响应数据为空，跳过处理。")
            return False

        analysis_data = None # 先声明变量

        try:
            # 提取核心的 AI 响应结果字符串
            ai_response = response_data.get('ai_response')
            if not ai_response or not isinstance(ai_response, dict):
                print(f"[WARN] Session {session_id}: 响应中缺少 'ai_response' 或格式不正确。响应: {response_data}")
                return False

            result_str = ai_response.get('result')
            if not result_str or not isinstance(result_str, str):
                print(f"[WARN] Session {session_id}: 'ai_response' 中缺少 'result' 字符串或格式不正确。响应: {ai_response}")
                return False

            # 尝试解析 result 字符串中的 JSON
            try:
                # 清理可能的 markdown 代码块标记
                cleaned_result_str = result_str.strip().strip('```json').strip('```').strip()
                analysis_data = json.loads(cleaned_result_str)
            except json.JSONDecodeError as e:
                print(f"[ERROR] Session {session_id}: 解析 'result' 中的 JSON 失败: {e}")
                print(f"原始 'result' 字符串 (前500字符): {result_str[:500]}...")
                # 保存包含错误的原始字符串，以便调试
                self._save_analysis(session_id, {"error": "JSONDecodeError", "details": str(e), "raw_result": result_str})
                return False

            # 保存解析后的 JSON 数据
            save_success = self._save_analysis(session_id, analysis_data)
            if not save_success:
                 return False # 如果保存失败，也认为处理失败
                 
            # 将数据存储到 StarRocks 数据库
            if self.starrocks_store and self.starrocks_store.conn:
                self.starrocks_store.store_complaint(session_id, analysis_data)

            # 检查是否需要发送告警 (仅当 JSON 解析和保存成功后)
            self._check_alert_conditions_and_notify(session_id, analysis_data, staff_name, customer_name, member_id)

            return True # 处理成功

        except Exception as e:
            print(f"[ERROR] Session {session_id}: 处理响应时发生意外错误: {e}")
            # 尝试保存错误信息和部分原始数据
            error_info = {"error": "Unexpected processing error", "details": str(e)}
            if analysis_data: # 如果 JSON 解析成功但后续出错
                error_info["parsed_analysis_data"] = analysis_data
            else: # 如果在 JSON 解析前出错
                error_info["raw_response_data"] = response_data
            self._save_analysis(session_id, error_info)
            return False


    def _check_alert_conditions_and_notify(self, session_id, analysis_data, staff_name="未知", customer_name="未知", member_id=0):
        """检查分析数据是否满足告警条件，如果满足则发送飞书告警。"""
        if not config.FEISHU_APP_ID or not config.FEISHU_APP_SECRET:
            print("[WARN] 未配置飞书自建应用的 appkey 或 secret，告警功能禁用。")
            return
        alert_triggered = False
        alert_reasons = []

        # 告警规则
        # {
        #     "用户诉求": {
        #         "用户核心诉求": "解决小盘股交易限制",
        #         "佐证": [
        #             "我想要解决方案",
        #             "我要解决问题才能交易",
        #             "我怎么交易"
        #         ]
        #     },
        #     "关键词匹配": {
        #         "匹配结论": true,
        #         "具体词汇及频次": {
        #             "限制我": 1,
        #             "都买不了": 2,
        #             "解决方法": 1
        #         }
        #     },
        #     "用户情绪": {
        #         "情绪指数": "重度负面情绪",
        #         "情绪类型": "失望",
        #         "强度判断": "重度",
        #         "佐证": [
        #             "理解没用，我要的是解决方法",
        #             "我想买的股票都买不了"
        #         ]
        #     },
        #     "对话模式": {
        #         "会话开始时间": "2025-04-22T17:53:04.225",
        #         "会话结束时间": "2025-04-22T18:09:50.910",
        #         "会话持续时长": "16",
        #         "对话轮次": 41,
        #         "客服处理效率": "低",
        #         "理由": "客服未能提供有效的解决方案，用户多次询问但未得到满意的答复，显示出客服处理效率低下。"
        #     },
        #     "风险点识别": {
        #         "投诉类型": "无",
        #         "投诉风险": "无风险",
        #         "佐证": []
        #     },
        #     "解决结果": {
        #         "客服处理结果": "未解决"
        #     },
        #     "规则判断": {
        #         "规则命中": "命中关键词+重度负面情绪",
        #         "规则命中等级": "三级"
        #     }
        # }

        try:
            # first of all 查看解决结果，未解决才继续判断，已解决过滤不需要告警
            solve_result = analysis_data.get('解决结果', {})
            deal_result = solve_result.get('客服处理结果')
            if deal_result == '已解决':
                print(f"[INFO] Session {session_id}: 客服已解决，跳过告警。")
                return
            # 检查规则判断是否命中规则且为一级/二级才告警
            rule_judgment = analysis_data.get('规则判断', {})
            rule_level = rule_judgment.get('规则命中等级')
            if rule_level in ['一级']:
                print(f"[INFO] Session {session_id}: 规则命中等级为一级，触发告警。")
                alert_reasons.append(rule_judgment.get('规则命中'))
    
                # 发送告警卡片
                self.feishu_notifier.send_alert(
                    session_id=session_id,
                    reason=", ".join(alert_reasons),
                    analysis_data=analysis_data,
                    staff_name=staff_name,
                    customer_name=customer_name,
                    rule_level=rule_level,
                    member_id=member_id
                )
    
                # 创建飞书任务
                summary = f"客服会话告警[{rule_level}]: {session_id}"
                description = (
                    f"客服: {staff_name}\n"
                    f"客户: {customer_name}\n"
                    f"会员ID: {member_id}\n"
                    f"告警原因: {', '.join(alert_reasons)}\n"
                    f"用户诉求: {analysis_data.get('用户诉求', {}).get('用户核心诉求', '')}"
                )
                alert_content = f"客服: {staff_name}，客户: {customer_name}，告警原因: {', '.join(alert_reasons)}"
                self.feishu_notifier.create_feishu_task(
                    summary=summary, 
                    description=description, 
                    session_id=session_id,
                    alert_type=f"{rule_level}告警",
                    alert_level="high",
                    alert_content=alert_content
                )

        except Exception as e:
            print(f"[ERROR] Session {session_id}: 检查告警条件或发送通知时出错: {e}")


    def _save_analysis(self, session_id, data):
        """
        将分析结果或处理过程中的信息保存到本次运行的子目录下的 JSON 文件。

        Args:
            session_id (str): 会话 ID，用作文件名。
            data (dict): 要保存的字典数据。

        Returns:
            bool: 保存是否成功。
        """
        try:
            # 使用 session_id 作为文件名，保存在 self.output_dir (已包含 run_identifier)
            filename = f"{session_id}.json"
            filepath = os.path.join(self.output_dir, filename)

            # 确保目录存在 (虽然 init 中创建过，但再次检查无害)
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # print(f"[DEBUG] Session {session_id}: 数据已保存至 {filepath}")
            return True
        except Exception as e:
            print(f"[ERROR] Session {session_id}: 保存数据到文件 {filepath} 时失败: {e}")
            return False
            
    def __del__(self):
        """析构函数，确保在对象销毁时断开数据库连接"""
        if hasattr(self, 'starrocks_store') and self.starrocks_store:
            self.starrocks_store.disconnect()