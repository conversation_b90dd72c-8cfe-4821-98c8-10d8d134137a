from flask import Flask, jsonify, request

import lark_oapi as lark
import json
from lark_oapi.adapter.flask import *
from lark_oapi.api.im.v1 import *
from lark_oapi.api.task.v1 import P2TaskTaskUpdatedV1
from lark_oapi.event.callback.model.p2_card_action_trigger import P2CardActionTrigger, P2CardActionTriggerResponse
from lark_oapi.event.callback.model.p2_url_preview_get import P2URLPreviewGet, P2URLPreviewGetResponse
import os
import datetime

from feishu_bot import FeishuBotNotifier
from store_data_to_sr import StarRocksStore
import config

app = Flask(__name__)
feishu_notifier = FeishuBotNotifier()
# 初始化数据库连接配置，但不立即连接
sr_store = StarRocksStore(
    host=config.STARROCKS_HOST,
    port=config.STARROCKS_PORT,
    user=config.STARROCKS_USER,
    password=config.STARROCKS_PASSWORD,
    database=config.STARROCKS_DATABASE
)

# 新版卡片回调，卡片回传交互 card.action.trigger
def do_card_action_trigger(data: P2CardActionTrigger) -> P2CardActionTriggerResponse:
    print(lark.JSON.marshal(data))
    resp = {
        "toast": {
            "type": "info",
            "content": "卡片回传成功 from python sdk"
        }
    }
    return P2CardActionTriggerResponse(resp)
# 拉取链接预览数据 url.preview.get
def do_url_preview_get(data: P2URLPreviewGet) -> P2URLPreviewGetResponse:
    print(lark.JSON.marshal(data))
    resp = {
        "inline": {
            "title": "链接预览测试",
        }
    }
    return P2URLPreviewGetResponse(resp)

# 添加任务状态变更事件处理函数
def do_p2_task_task_updated_v1(data: P2TaskTaskUpdatedV1) -> None:
    """处理飞书任务状态变更事件"""
    try:
        print(f"[INFO] 收到任务状态变更事件: {lark.JSON.marshal(data)}")
        
        # 获取任务ID和通知类型
        feishu_task_id = data.event.task_id
        obj_type = data.event.obj_type
        
        # 根据obj_type判断任务状态
        task_status = "completed" if obj_type == 5 else "pending"
        
        print(f"[INFO] 任务 {feishu_task_id} 状态变更为: {task_status}")
        
        # 连接数据库
        if not sr_store.connect():
            print("[ERROR] 连接数据库失败，无法更新任务状态")
            return
            
        # 直接使用 update_task_status 方法更新状态，但不要关闭连接
        sr_store.update_task_status(feishu_task_id, task_status)
        
    except Exception as e:
        print(f"[ERROR] 处理任务状态变更事件时出错: {e}")


def do_p2_im_message_receive_v1(data: P2ImMessageReceiveV1) -> None:
    """处理@机器人的消息"""
    print(f"[DEBUG] 收到消息: {data.event.message}")
    
    if not data.event.message.mentions:
        print("[DEBUG] 没有@提及信息，跳过处理")
        return
        
    for mention in data.event.message.mentions:
        print(f"[DEBUG] 提及信息: key={mention.key}, name={mention.name}")
        # 修改这里，仅检查name是否为chat-bot，不需要检查@符号
        if mention.name == config.FEISHU_BOT_NAME:
            try:
                content = json.loads(data.event.message.content)
                text = content.get("text", "")
                print(f"[DEBUG] 提取到消息文本: {text}")
                
                # 修改提取逻辑，根据mention.key来分割文本
                # @_user_1 在文本中是以 mention.key 的形式出现的
                parts = text.split(mention.key)
                if len(parts) > 1:
                    session_id = parts[-1].strip()
                    if not session_id:
                        feishu_notifier.send_card({
                            "config": {"wide_screen_mode": True},
                            "header": {"title": {"content": "请提供会话ID", "tag": "plain_text"}},
                            "elements": [{
                                "tag": "div",
                                "text": {"content": "请提供会话ID，格式: @chat-bot 会话ID", "tag": "plain_text"}
                            }]
                        })
                        return
                    
                    print(f"[DEBUG] 提取到会话ID: {session_id}")
                    
                    # 获取会话记录
                    try:
                        from qiyu_session_service import get_session_messages, format_session_messages
                        messages = get_session_messages(session_id)
                        if not messages:
                            feishu_notifier.send_card({
                                "config": {"wide_screen_mode": True},
                                "header": {"title": {"content": "未找到会话记录", "tag": "plain_text"}},
                                "elements": [{
                                    "tag": "div",
                                    "text": {"content": f"未找到会话ID为 {session_id} 的记录", "tag": "plain_text"}
                                }]
                            })
                            return
                            
                        formatted = format_session_messages(messages)
                        print(f"[DEBUG] 会话记录: {formatted}")
                        # formatted是字符串列表，直接连接即可
                        formatted_text = "\n".join(formatted)
                        
                        # 发送会话详情卡片
                        feishu_notifier.send_card({
                            "config": {"wide_screen_mode": True},
                            "header": {"title": {"content": f"会话 {session_id} 详情", "tag": "plain_text"}},
                            "elements": [{
                                "tag": "div",
                                "text": {"content": formatted_text, "tag": "plain_text"}
                            }]
                        })
                        
                        print(f"[DEBUG] 成功处理会话查询: {session_id}")
                    except ImportError as e:
                        print(f"[ERROR] 导入qiyu_session_service模块失败: {e}")
                        feishu_notifier.send_card({
                            "config": {"wide_screen_mode": True},
                            "header": {"title": {"content": "系统错误", "tag": "plain_text"}},
                            "elements": [{
                                "tag": "div",
                                "text": {"content": "系统错误：无法访问会话服务", "tag": "plain_text"}
                            }]
                        })
                else:
                    feishu_notifier.send_card({
                        "config": {"wide_screen_mode": True},
                        "header": {"title": {"content": "请提供会话ID", "tag": "plain_text"}},
                        "elements": [{
                            "tag": "div",
                            "text": {"content": "请提供会话ID，格式: @chat-bot 会话ID", "tag": "plain_text"}
                        }]
                    })
                
            except Exception as e:
                print(f"[ERROR] 处理消息时出错: {e}")
                feishu_notifier.send_card({
                    "config": {"wide_screen_mode": True},
                    "header": {"title": {"content": "处理错误", "tag": "plain_text"}},
                    "elements": [{
                        "tag": "div",
                        "text": {"content": f"处理消息时出错: {str(e)}", "tag": "plain_text"}
                    }]
                })

# 注册事件处理程序，添加任务状态变更事件的监听
handler = lark.EventDispatcherHandler.builder(
    config.FEISHU_ENCRYPT_KEY, 
    config.FEISHU_VERIFICATION_TOKEN, 
    lark.LogLevel.DEBUG
).register_p2_im_message_receive_v1(do_p2_im_message_receive_v1)\
    .register_p2_task_task_updated_v1(do_p2_task_task_updated_v1).build()

@app.route("/v1/public/event", methods=["POST"])
def event():
    try:
        # 获取原始请求数据
        raw_body = request.get_data(as_text=True)
        print(f"[DEBUG] 收到原始请求体: {raw_body}")
        
        # 先处理飞书的验证请求
        json_data = request.get_json()
        if 'challenge' in json_data:
            print(f"[DEBUG] 收到飞书验证请求，challenge: {json_data['challenge']}")
            return jsonify({'challenge': json_data['challenge']})

        # 处理常规回调事件
        resp = handler.do(parse_req())
        return parse_resp(resp)
    except Exception as e:
        print(f"[ERROR] 处理请求时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        # 确保在处理完请求后断开数据库连接，避免连接泄漏
        try:
            if sr_store and sr_store.conn and sr_store.conn.open:
                sr_store.disconnect()
                print("[INFO] 已断开与 StarRocks 数据库的连接")
        except Exception as e:
            print(f"[WARN] 断开数据库连接时出错: {e}")

if __name__ == "__main__":
    app.run(port=8080)