# 帮助中心主题数据导出说明

## 功能说明
新增了 `query_and_export_helpcenter_topics()` 函数，用于导出帮助中心主题数据到CSV文件。

## 使用方法

### 1. 导出帮助中心主题数据
```bash
python sr_export.py helpcenter
```

### 2. 导出问题对数据（原有功能）
```bash
python sr_export.py
```

## SQL查询说明
导出的帮助中心数据包含以下字段：
- `id`: 主题ID
- `title_cn`: 中文标题
- `title_en`: 英文标题  
- `title_hk`: 繁体中文标题
- `app_id`: 应用ID
- `body_cn`: 中文内容
- `body_en`: 英文内容
- `body_hk`: 繁体中文内容

## 查询条件
- 只查询 `app_id` 为 'longbridge' 或 'longbridge_sg' 的数据
- 限制返回100条记录（LIMIT 100 OFFSET 0）
- 按主题ID升序排列

## 输出文件
导出的CSV文件命名格式：`helpcenter_topics_YYYYMMDD_HHMMSS.csv`

## 注意事项
1. 确保数据库连接配置正确
2. 确保有访问 `hive.lb_helpcenter_prod` 数据库的权限
3. 如需修改查询条件（如LIMIT数量），请直接修改 `sr_export.py` 中的SQL语句
