include:
  - https://git.5th.im/open-source/ci-template/-/raw/master/templates/docker-login.yaml
  - https://git.5th.im/open-source/ci-template/raw/master/templates/deployment-gitlab.yml
  #  - https://git.5th.im/open-source/ci-template/raw/release/templates/deployment-gitlab.yml
  - https://git.5th.im/open-source/ci-template/raw/release/templates/ecr-image-push.yml
  - https://git.5th.im/open-source/ci-template/-/raw/master/templates/maersk-template.yml
variables:
  NAMESPACE: bi
  SERVER: https://master-api.cluster.5th.im:8443
  APP_NAME: vox-inspect
  REPOSITORY: docker.longbridge-inc.com
  ##文件服务模块
  DOCKER_PATH: Dockerfile

image: docker.longbridge-inc.com/long-bridge-bi/bi-node-builder:12.0.0
stages:
  - build
  - push
  - deploy

#############################################################build#############################################################
build-staging-hk:
  extends: .docker-login
  stage: build
  only:
    - staging
  variables:
    TAG: hk-$APP_NAME-$CI_COMMIT_SHA
  script:
    - docker build -f $DOCKER_PATH -t $REPOSITORY/long-bridge-bi/$APP_NAME:hk-$APP_NAME-$CI_COMMIT_SHA --build-arg ENV_TYPE=staging .
    - docker push $REPOSITORY/long-bridge-bi/$APP_NAME:hk-$APP_NAME-$CI_COMMIT_SHA
#############################################################push#############################################################
push-staging-hk:
  extends:
    - .docker-login
    - .push_template
  variables:
    IMAGE_NAME: long-bridge-bi/$APP_NAME
    IMAGE_HOST: $REPOSITORY
    ENV: canary-hk
    TAG: hk-$APP_NAME-$CI_COMMIT_SHA
  only:
    - staging
#############################################################deploy#############################################################
maersk-deploy-staging:
  extends: .maersk-template
  stage: deploy
  only:
    - staging
  variables:
    APP: $APP_NAME
    CLUSTER: staging-ack-1
    NAMESPACE: bi
    DEPLOY: $APP_NAME
    CONTAINER: $APP_NAME
    VERSION: hk-$APP_NAME-$CI_COMMIT_SHA

#############################################################build#############################################################
build-prod-hk:
  extends: .docker-login
  stage: build
  only:
    - prod
  variables:
    TAG: hk-$APP_NAME-$CI_COMMIT_SHA
  script:
    - docker build -f $DOCKER_PATH -t $REPOSITORY/long-bridge-bi/$APP_NAME:hk-$APP_NAME-$CI_COMMIT_SHA --build-arg ENV_TYPE=staging .
    - docker push $REPOSITORY/long-bridge-bi/$APP_NAME:hk-$APP_NAME-$CI_COMMIT_SHA
#############################################################push#############################################################
push-prod-hk:
  extends:
    - .docker-login
    - .push_template
  variables:
    IMAGE_NAME: long-bridge-bi/$APP_NAME
    IMAGE_HOST: $REPOSITORY
    ENV: canary-hk
    TAG: hk-$APP_NAME-$CI_COMMIT_SHA
  only:
    - prod
#############################################################deploy#############################################################
maersk-deploy-prod:
  extends: .maersk-template
  stage: deploy
  only:
    - prod
  variables:
    APP: $APP_NAME
    CLUSTER: prod-hk-ack-1
    NAMESPACE: bi
    DEPLOY: $APP_NAME
    CONTAINER: $APP_NAME
    VERSION: hk-$APP_NAME-$CI_COMMIT_SHA
