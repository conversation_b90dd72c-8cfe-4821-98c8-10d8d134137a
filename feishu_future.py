import time

import requests
import json
import config
import datetime
import lark_oapi as lark

from lark_oapi.api.docx.v1 import *

from feishu_bot import FeishuBotNotifier


def create_document(notifier: FeishuBotNotifier, title: str, content: str):
    """
    创建飞书文档

    Args:
        notifier (FeishuBotNotifier): 用于发送飞书消息的实例
        title (str): 文档标题
        content (str): 文档内容

    Returns:
        str: 创建的文档ID，失败返回None
    """
    try:
        # 创建client
        client = lark.Client.builder() \
            .app_id(config.FEISHU_APP_ID) \
            .app_secret(config.FEISHU_APP_SECRET) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

        # 构造请求对象
        request: CreateDocumentRequest = CreateDocumentRequest.builder() \
            .request_body(CreateDocumentRequestBody.builder()
                          .folder_token("fldcnqquW1svRIYVT2Np6Iabcef")  # 文档所在文件夹的 token
                          .title(title)  # 使用传入的标题
                          .build()) \
            .build()

        # 发起请求
        response: CreateDocumentResponse = client.docx.v1.document.create(request)

        # 处理失败返回
        if not response.success():
            print(f"[ERROR] 创建飞书文档失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}")
            return None

        # 获取创建的文档ID
        document_id = response.data.document.document_id
        print(f"[INFO] 成功创建飞书文档，文档ID: {document_id}")

        # 目前飞书文档 API 还不支持直接写入内容，需要后续调用其他 API
        
        return document_id
        
    except Exception as e:
        print(f"[ERROR] 创建飞书文档时发生错误: {e}")
        return None

if __name__ == '__main__':
    notifier = FeishuBotNotifier()
    create_document(notifier, "测试文档", "这是一篇测试文档")

