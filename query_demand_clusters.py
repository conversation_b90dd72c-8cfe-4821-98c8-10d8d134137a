#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查询用户诉求聚类结果
"""

import json
import pandas as pd
from datetime import datetime
from typing import Optional, List

try:
    import lancedb
    LANCEDB_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 lancedb: pip install lancedb")
    LANCEDB_AVAILABLE = False


class DemandClusterQuery:
    """用户诉求聚类结果查询器"""
    
    def __init__(self):
        if LANCEDB_AVAILABLE:
            self.db = lancedb.connect("./user_demand_clustering_db")
        else:
            self.db = None
            print("[ERROR] LanceDB 不可用")
    
    def list_tables(self):
        """列出所有表"""
        if not self.db:
            return []
        return self.db.table_names()
    
    def query_clusters(self, table_name: str = "user_demand_clusters",
                      min_frequency: int = 1,
                      min_cluster_size: int = 1) -> pd.DataFrame:
        """查询聚类结果
        
        Args:
            table_name: 表名
            min_frequency: 最小频次
            min_cluster_size: 最小聚类大小
            
        Returns:
            聚类结果DataFrame
        """
        if not self.db:
            print("[ERROR] LanceDB 不可用")
            return pd.DataFrame()
        
        try:
            table = self.db.open_table(table_name)
            
            # 查询所有数据
            df = table.to_pandas()
            
            # 应用过滤条件
            df = df[
                (df['total_frequency'] >= min_frequency) &
                (df['cluster_size'] >= min_cluster_size)
            ].copy()
            
            # 按总频次排序
            df = df.sort_values('total_frequency', ascending=False)
            
            return df
            
        except Exception as e:
            print(f"[ERROR] 查询失败: {e}")
            return pd.DataFrame()
    
    def search_demands(self, query: str, table_name: str = "user_demand_clusters") -> pd.DataFrame:
        """搜索包含特定关键词的诉求聚类
        
        Args:
            query: 搜索关键词
            table_name: 表名
            
        Returns:
            搜索结果DataFrame
        """
        df = self.query_clusters(table_name)
        
        if df.empty:
            return df
        
        # 在代表性诉求和所有诉求中搜索
        mask = (
            df['representative_demand'].str.contains(query, case=False, na=False) |
            df['all_demands'].astype(str).str.contains(query, case=False, na=False)
        )
        
        return df[mask]
    
    def get_cluster_details(self, cluster_id: int, 
                           table_name: str = "user_demand_clusters") -> dict:
        """获取特定聚类的详细信息
        
        Args:
            cluster_id: 聚类ID
            table_name: 表名
            
        Returns:
            聚类详细信息
        """
        df = self.query_clusters(table_name)
        
        cluster_row = df[df['cluster_id'] == cluster_id]
        
        if cluster_row.empty:
            return {}
        
        return cluster_row.iloc[0].to_dict()
    
    def print_summary(self, table_name: str = "user_demand_clusters"):
        """打印聚类结果摘要"""
        df = self.query_clusters(table_name)
        
        if df.empty:
            print("没有找到聚类数据")
            return
        
        print(f"用户诉求聚类结果摘要 (表: {table_name})")
        print("=" * 60)
        print(f"总聚类数: {len(df)}")
        print(f"总诉求数: {df['cluster_size'].sum()}")
        print(f"总频次: {df['total_frequency'].sum()}")
        print(f"平均聚类大小: {df['cluster_size'].mean():.1f}")
        print(f"平均相似度: {df['avg_similarity'].mean():.3f}")
        
        print(f"\n前100个主要诉求类别:")
        print("-" * 60)
        
        for i, (_, row) in enumerate(df.head(100).iterrows(), 1):
            print(f"{i:2d}. [{row['cluster_size']:2d}个] {row['representative_demand']}")
            print(f"    频次: {row['total_frequency']}, 相似度: {row['avg_similarity']:.3f}")


def main():
    """主函数"""
    query_tool = DemandClusterQuery()
    
    # 列出可用表
    tables = query_tool.list_tables()
    print(f"可用表: {tables}")
    
    if not tables:
        print("没有找到聚类结果表，请先运行聚类分析")
        return
    
    # 使用最新的表
    latest_table = tables[-1] if tables else "user_demand_clusters"
    
    # 打印摘要
    query_tool.print_summary(latest_table)
    
    # 交互式查询示例
    print(f"\n" + "=" * 60)
    print("交互式查询示例:")
    print("=" * 60)
    
    # 搜索示例
    search_keywords = ["退款", "客服", "订单", "账户"]
    for keyword in search_keywords:
        results = query_tool.search_demands(keyword, latest_table)
        if not results.empty:
            print(f"\n🔍 搜索 '{keyword}' 的结果:")
            for _, row in results.head(3).iterrows():
                print(f"  - [{row['cluster_size']}个] {row['representative_demand']} (频次: {row['total_frequency']})")


if __name__ == "__main__":
    main() 