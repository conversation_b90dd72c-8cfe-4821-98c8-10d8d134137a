import time

import requests
import json
import config

import datetime
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.api.task.v2 import *
from store_data_to_sr import StarRocksStore

# Function to create the analysis card dictionary
def create_analysis_card(analysis_data, session_id, reason, staff_name="未知", customer_name="未知", rule_level="无", member_id=0):
    """
    根据分析数据构建飞书卡片消息的字典结构。

    Args:
        analysis_data (dict): 大模型返回的分析结果字典。
        session_id (str): 会话 ID。
        reason (str): 触发告警的原因。
        staff_name (str): 客服名称。
        customer_name (str): 客户名称。
        rule_level (str): 告警等级。
        member_id (int): 会员ID。

    Returns:
        dict: 飞书卡片消息的字典结构。
    """
    # --- 用户诉求处理 ---
    user_request_fields = [
        {"is_short": False, "text": {"content": "**用户诉求**", "tag": "lark_md"}}
    ]
    if analysis_data.get('用户诉求', {}).get('用户核心诉求'):
        user_request_fields.append({
            "is_short": False,
            "text": {"content": f"核心诉求: {analysis_data['用户诉求']['用户核心诉求']}", "tag": "lark_md"}
        })
    if analysis_data.get('用户诉求', {}).get('佐证'):
        user_request_fields.append({
            "is_short": False,
            "text": {"content": f"佐证: \"{analysis_data['用户诉求']['佐证']}\"", "tag": "lark_md"}
        })

    # --- 对话模式处理 ---
    dialogue_mode = analysis_data.get('对话模式', {})
    dialogue_fields = [
        {"is_short": False, "text": {"content": "**会话基本信息**", "tag": "lark_md"}},
        {"is_short": True, "text": {"content": f"会话开始时间: {dialogue_mode.get('会话开始时间', '未知')}", "tag": "lark_md"}},
        {"is_short": True, "text": {"content": f"会话结束时间: {dialogue_mode.get('会话结束时间', '未知')}", "tag": "lark_md"}},
        {"is_short": True, "text": {"content": f"会话持续时长: {dialogue_mode.get('会话持续时长', '未知')} 分钟", "tag": "lark_md"}},
        {"is_short": False, "text": {"content": "**对话模式**", "tag": "lark_md"}},
        {"is_short": True, "text": {"content": f"对话轮次: {dialogue_mode.get('对话轮次', '未知')}", "tag": "lark_md"}},
        {"is_short": True, "text": {"content": f"客服效率: {dialogue_mode.get('客服处理效率', '未知')}", "tag": "lark_md"}},
    ]
    # 只有理由存在时才显示
    if dialogue_mode.get('理由'):
        dialogue_fields.append({"is_short": False, "text": {"content": f"理由: {dialogue_mode.get('理由')}", "tag": "lark_md"}})

    # --- 风险点识别处理 ---
    risk_fields = [
        {"is_short": False, "text": {"content": "**风险点识别**", "tag": "lark_md"}}
    ]
    risk_identification = analysis_data.get('风险点识别', {})
    if risk_identification:
        risk_fields.append({"is_short": True, "text": {"content": f"投诉风险: {risk_identification.get('投诉风险', '未知')}", "tag": "lark_md"}})
        risk_fields.append({"is_short": True, "text": {"content": f"投诉类型: {risk_identification.get('投诉类型', '未知')}", "tag": "lark_md"}})
        risk_fields.append({"is_short": True, "text": {"content": f"投诉渠道: {risk_identification.get('投诉渠道', '未知')}", "tag": "lark_md"}})

    # --- 卡片整体结构 ---
    # 匹配不同的 rule_level，设置不同的卡片颜色
    if rule_level == '一级':
        card_color = "red"
    elif rule_level == '二级':
        card_color = "orange"
    elif rule_level == '三级':
        card_color = "blue"
    else:
        card_color = "red"  # 默认为红色
    card = {
        "config": {"wide_screen_mode": True},
        "header": {
            "template": card_color, # 使用红色标题栏表示告警
            "title": {"content": f"🚨 客服会话告警: {reason}", "tag": "plain_text"}
        },
        "elements": [
            # 会话基本信息
            {
                "tag": "div",
                "fields": [
                    {"is_short": True, "text": {"content": f"**会话ID:**{session_id}", "tag": "lark_md"}},
                    {"is_short": True, "text": {"content": f"**告警时间:**{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", "tag": "lark_md"}},
                    {"is_short": True, "text": {"content": f"**客服:**{staff_name}", "tag": "lark_md"}},
                    {"is_short": True, "text": {"content": f"**客户:**{customer_name}", "tag": "lark_md"}},
                    {"is_short": True, "text": {"content": f"**MemberID:**{member_id}", "tag": "lark_md"}}
                ]
            },
            {"tag": "hr"},
            {"tag": "div", "fields": user_request_fields},
            {"tag": "hr"},
            {"tag": "div", "fields": dialogue_fields},
            {"tag": "hr"},
            {"tag": "div", "fields": risk_fields}
        ]
    }
    return card


class FeishuBotNotifier:
    """用于向飞书机器人发送消息的类。"""

    def __init__(self, app_id=None, app_secret=None):
        """
        初始化 FeishuBotNotifier。

        Args:
            app_secret (str): 飞书应用的 App Secret，用于验证回调签名
        """
        self.app_id = config.FEISHU_APP_ID  # 飞书应用 ID
        self.app_secret = config.FEISHU_APP_SECRET  # 飞书应用密钥
        self.chat_id = config.FEISHU_CHAT_ID
        
        # Token 相关
        self.tenant_access_token = None
        self.token_expire_time = None
        
        if not self.app_secret:
            print("[WARN] 未配置飞书应用密钥，将无法使用自建应用功能")

    def _get_tenant_access_token(self):
        """获取 tenant_access_token 并处理刷新逻辑"""
        if self.tenant_access_token and datetime.datetime.now() < self.token_expire_time:
            return self.tenant_access_token
            
        try:
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            headers = {'Content-Type': 'application/json'}
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }
            
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                self.tenant_access_token = result["tenant_access_token"]
                # 提前5分钟刷新token
                self.token_expire_time = datetime.datetime.now() + datetime.timedelta(seconds=result["expire"] - 300)
                return self.tenant_access_token
            else:
                print(f"[ERROR] 获取 tenant_access_token 失败: {result}")
                return None
        except Exception as e:
            print(f"[ERROR] 获取 tenant_access_token 时发生错误: {e}")
            return None

    def send_card(self, card_content):
        """
        使用自建应用发送飞书卡片消息
        
        Args:
            card_content (dict): 卡片内容的JSON结构，支持1.0和2.0版本
            
        Returns:
            bool: 发送是否成功
        """
        if not self.app_secret:
            print("[ERROR] 未配置飞书应用密钥，无法使用自建应用发送消息")
            return False
            
        try:
            # 创建client
            client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .log_level(lark.LogLevel.DEBUG) \
                .build()
            
            # 根据卡片版本构造Content内容
            card_json = json.dumps(card_content)
            
            # 构造请求对象
            request = CreateMessageRequest.builder() \
                .receive_id_type("chat_id") \
                .request_body(CreateMessageRequestBody.builder()
                    .receive_id(self.chat_id)
                    .msg_type("interactive")
                    .content(card_json)
                    .build()) \
                .build()

            # 发起请求
            response = client.im.v1.message.create(request)

            # 处理失败返回
            if not response.success():
                print(f"[ERROR] 发送飞书卡片消息失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}")
                return False

            print(f"[INFO] 成功发送飞书卡片消息。消息ID: {response.data.message_id}")
            return True
            
        except Exception as e:
            print(f"[ERROR] 发送飞书卡片消息时发生错误: {e}")
            return False

    def send_alert(self, session_id, reason, analysis_data: dict, staff_name="未知", customer_name="未知", rule_level="无", member_id=0):
        """
        发送包含会话分析结果的飞书卡片告警消息（使用自建应用）
        
        Args:
            session_id (str): 触发告警的会话 ID
            reason (str): 触发告警的原因描述
            analysis_data (dict): 大模型分析结果的字典
            staff_name (str): 客服名称，默认为"未知"
            customer_name (str): 客户名称，默认为"未知"
            rule_level (str): 告警等级
            member_id (int): 会员ID，默认为0

        Returns:
            bool: 发送是否成功
        """
        if not self.app_secret:
            print("[WARN] 飞书应用密钥未配置，跳过发送告警卡片。")
            return False

        if not isinstance(analysis_data, dict):
            print(f"[ERROR] analysis_data 必须是字典类型，但收到了 {type(analysis_data)} 类型。无法生成卡片。")
            return False

        try:
            # 调用函数生成卡片字典
            card_dict = create_analysis_card(
                analysis_data=analysis_data,
                session_id=session_id,
                reason=reason,
                staff_name=staff_name,
                customer_name=customer_name,
                rule_level=rule_level,
                member_id=member_id
            )
            
            # 使用自建应用发送卡片
            return self.send_card(card_dict)
        except Exception as e:
            print(f"[ERROR] 生成或发送告警卡片时发生错误: {e}")
            return False

    def reply_message(self, message_id, content):
        """
        回复飞书消息。

        Args:
            message_id (str): 要回复的消息的 ID
            content (str): 回复的内容

        Returns:
            bool: 发送是否成功
        """
        if not self.app_secret:
            print("[ERROR] 未配置飞书应用密钥，无法使用自建应用发送回复消息")
            return False

        try:
            # 创建client
            client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .log_level(lark.LogLevel.DEBUG) \
                .build()

            request: ReplyMessageRequest = ReplyMessageRequest.builder() \
                .message_id(message_id) \
                .request_body(ReplyMessageRequestBody.builder()
                    .msg_type("text")
                    .content(content)
                    .reply_in_thread(True)
                    .build()) \
                .build()

            # 发起请求
            response: ReplyMessageResponse = client.im.v1.message.reply(request)

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.im.v1.message.reply failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
                return

            # 处理业务结果
            lark.logger.info(lark.JSON.marshal(response.data, indent=4))
            print(f"[INFO] 创建回复消息成功。回复消息ID: {response.data.message_id}")

        except Exception as e:
            print(f"[ERROR] 发送飞书回复消息时发生错误 : {e}")
            return False

    def _format_analysis_data(self, analysis_data):
        """
        格式化分析数据为人类可读的字符串。

        Args:
            analysis_data (dict): 分析结果字典。

        Returns:
            str: 格式化后的字符串。
        """
        formatted_string = "\n--- 分析摘要 ---\n"
        for key, value in analysis_data.items():
            formatted_string += f"**{key}:**\n"
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    formatted_string += f"  - {sub_key}: {sub_value}\n"
            else:
                formatted_string += f"  {value}\n"
        return formatted_string

    def _get_current_time(self):
        """获取当前北京时间字符串。"""
        beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
        return datetime.datetime.now(beijing_tz).strftime("%Y-%m-%d %H:%M:%S")

    def create_feishu_task(self, summary, description, session_id=None, alert_type="一级告警", alert_level="high", alert_content=None):
        """创建飞书任务
        
        Args:
            summary (str): 任务摘要
            description (str): 任务描述
            session_id (str): 关联的会话ID
            alert_type (str): 告警类型，默认为"一级告警"
            alert_level (str): 告警级别，默认为"high"
            alert_content (str): 告警内容，如果为None则使用description
            
        Returns:
            str: 创建的任务ID，失败返回None
        """
        if not self.app_secret:
            print("[WARN] 飞书应用密钥未配置，跳过创建任务")
            return None

        try:
            client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .log_level(lark.LogLevel.DEBUG) \
                .build()

            # 从数据库获取当前值班人员
            store = StarRocksStore(
                host=config.STARROCKS_HOST,
                port=config.STARROCKS_PORT,
                user=config.STARROCKS_USER,
                password=config.STARROCKS_PASSWORD,
                database=config.STARROCKS_DATABASE
            )
            
            if not store.connect():
                print("[ERROR] 连接数据库失败，无法获取值班人员")
                return None
                
            duty_staff = store.get_active_duty_staff()
            if not duty_staff:
                print("[WARN] 当前没有值班人员")
                return None

            # 只有一个值班人员时，直接分配
            if len(duty_staff) == 1:
                assigned_staff = duty_staff[0]
                print(f"[INFO] 只有一个值班人员 {assigned_staff['staff_name']}，直接分配任务")
            else:
                # 轮询分配任务：查询过去24小时内每个值班人员的任务数量
                try:
                    # 构建查询统计SQL
                    staff_names = []
                    for staff in duty_staff:
                        staff_names.append(staff['staff_name'])
                        
                    placeholders = ', '.join(['%s'] * len(staff_names))
                    query = f"""
                    SELECT assigned_staff, COUNT(*) as task_count
                    FROM alert_tasks
                    WHERE assigned_staff IN ({placeholders})
                    AND create_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY assigned_staff
                    ORDER BY task_count ASC
                    """
                    
                    store.cursor.execute(query, staff_names)
                    results = store.cursor.fetchall()
                    
                    # 初始化结果字典，确保所有值班人员都有记录
                    staff_task_counts = {staff['staff_name']: 0 for staff in duty_staff}
                    
                    # 更新任务计数
                    for row in results:
                        staff_name = row[0]
                        staff_task_counts[staff_name] = row[1]
                    
                    # 找出任务数量最少的值班人员
                    min_task_staff = min(staff_task_counts.items(), key=lambda x: x[1])
                    selected_staff_name = min_task_staff[0]
                    
                    # 找到对应的值班人员完整信息
                    assigned_staff = next(staff for staff in duty_staff if staff['staff_name'] == selected_staff_name)
                    
                    print(f"[INFO] 轮询分配任务: 选择任务最少的值班人员 {selected_staff_name}，当前任务数: {min_task_staff[1]}")
                    
                except Exception as e:
                    # 轮询算法出错，简单地选择第一个值班人员
                    assigned_staff = duty_staff[0]
                    print(f"[WARN] 轮询分配出错: {e}，默认选择第一个值班人员 {assigned_staff['staff_name']}")

            # 构建成员列表 - 只包含选中的值班人员
            members = [
                Member.builder()
                    .id(assigned_staff['open_id'])
                    .role("assignee")
                    .build()
            ]

            # 构建任务请求
            request = CreateTaskRequest.builder() \
                .user_id_type("open_id") \
                .request_body(InputTask.builder()
                    .summary(summary)
                    .description(description)
                    ## 设置截止时间为 72 小时后
                    .due(Due.builder()
                         .timestamp(int(time.time() + 72 * 60 * 60) * 1000)  # 转换为毫秒级时间戳
                         .build()
                         )
                    # 设置开始时间为当前时间
                    .start(Start.builder()
                           .timestamp(int(time.time()) * 1000)  # 转换为毫秒级时间戳
                           .build()
                           )
                    .tasklists([TaskInTasklistInfo.builder()
                               .tasklist_guid(config.FEISHU_TASKLIST_GUID)
                               .build()
                               ])
                    .members(members)
                    .build()) \
                .build()

            response = client.task.v2.task.create(request)
            if not response.success():
                print(f"[ERROR] 创建飞书任务失败: code={response.code}, msg={response.msg}")
                return None

            feishu_task_id = response.data.task.guid
            print(f"[INFO] 成功创建飞书任务，任务ID: {feishu_task_id}")

            # # 添加任务到任务清单 清单 GUID: 1d36dc9c-9212-4472-a3af-957cc4610b82
            # task_list_request: AddTasklistTaskRequest = AddTasklistTaskRequest.builder() \
            #     .task_guid(feishu_task_id) \
            #     .user_id_type("open_id") \
            #     .request_body(AddTasklistTaskRequestBody.builder()
            #                   .tasklist_guid(config.FEISHU_TASKLIST_GUID)
            #                   .build())\
            #     .build()
            #
            # task_list_response: AddTasklistTaskResponse = client.task.v2.task.add_tasklist(task_list_request)
            # if not task_list_response.success():
            #     print(f"[ERROR] 添加任务到清单失败: code={task_list_response.code}, msg={task_list_response.msg}")
            #     return None
            # print(f"[INFO] 添加任务到清单成功")

            # 如果告警内容为空，使用描述作为内容
            if alert_content is None:
                alert_content = description
            
            # 只为选中的值班人员创建告警任务记录
            task_created = False
            
            # 创建告警任务记录
            if store.create_alert_task(
                session_id=session_id, 
                alert_type=alert_type, 
                alert_level=alert_level, 
                alert_content=alert_content, 
                assigned_staff=assigned_staff['staff_name'],
                open_id=assigned_staff['open_id'],
                feishu_task_id=feishu_task_id
            ):
                # 更新告警任务的feishu_task_id
                store.update_feishu_task_id(session_id, assigned_staff['staff_name'], feishu_task_id)
                task_created = True
                print(f"[INFO] 已为值班人员 {assigned_staff['staff_name']} 创建告警任务记录")
            else:
                print(f"[ERROR] 为值班人员 {assigned_staff['staff_name']} 创建告警任务记录失败")
            
            # 关闭数据库连接
            store.disconnect()
            
            # 如果任务记录创建成功，则返回飞书任务ID
            if task_created:
                return feishu_task_id
            else:
                print("[ERROR] 创建告警任务记录失败")
                return None

        except Exception as e:
            print(f"[ERROR] 创建飞书任务时发生错误: {e}")
            return None

    # 创建任务清单
    def create_tasklist(self,task_list_name: str):
        """
        创建任务清单
        Returns:
            str: 创建的清单 GUID，失败返回 None
        """
        if not self.app_secret:
            print("[WARN] 飞书应用密钥未配置，跳过创建清单")
            return None

        try:
            client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .log_level(lark.LogLevel.DEBUG) \
                .build()

            # 构造请求对象
            request: CreateTasklistRequest = CreateTasklistRequest.builder() \
                .user_id_type("open_id") \
                .request_body(InputTasklist.builder()
                              .name(task_list_name)
                              .members([Member.builder()
                                       .id("ou_256d27d1f3101afffc17362bfffd56b7")
                                       .type("user")
                                       .role("editor")
                                       .build()
                                        ])
                              .build()) \
                .build()

            response = client.task.v2.tasklist.create(request)
            if not response.success():
                print(f"[ERROR] 创建任务清单失败: code={response.code}, msg={response.msg}")
                return None

            tasklist_guid = response.data.tasklist.guid
            print(f"[INFO] 成功创建任务清单，清单 GUID: {tasklist_guid}")
            return tasklist_guid

        except Exception as e:
            print(f"[ERROR] 创建任务清单时发生错误: {e}")
            return None
    
# 获取任务清单


# 可以在这里添加一些测试代码
if __name__ == '__main__':
    # 示例：测试发送卡片
    # 需要配置 config.py 中的 appid
    if config.FEISHU_APP_ID:
        notifier = FeishuBotNotifier()

        notifier.create_tasklist("质检告警任务清单") # 清单 GUID: 1d36dc9c-9212-4472-a3af-957cc4610b82 测试
        # 示例分析数据
        # sample_analysis = {
        #     "关键词匹配": {
        #         "匹配结论": 'true',
        #         "具体词汇及频次": {
        #             "慢": 2,
        #             "垃圾": 1,
        #             "套壳": 2,
        #             "成交不了": 3
        #         }
        #     },
        #     "用户情绪": {
        #         "情绪指数": "重度负面情绪",
        #         "情绪类型": "愤怒",
        #         "强度判断": "重度",
        #         "佐证": [
        #             "你们券商对比其他券商就是垃圾的多",
        #             "谁还敢来你们长桥啊...",
        #             "你要我这么帮你们宣传是吗?"
        #         ]
        #     },
        #     "对话模式": {
        #             "对话轮次": 11,
        #             "客服处理效率": "低",
        #             "理由": "客服多次使用自动回复模板（如超时关闭提醒），对用户连续提出的交易异常、系统延迟等核心问题未给出实质性解决方案，导致用户情绪升级。"
        #         },
        #     "风险点识别": {
        #             "投诉类型": "媒体投诉/法律风险",
        #             "投诉风险": "重度风险",
        #         "佐证": [
        #             "你要我这么帮你们宣传是吗?",
        #             "谁还敢来你们长桥啊...",
        #             "都说你长桥是套壳的，底层逻辑不是自己的"
        #         ]
        #     }
        # }
        #
        # print("\n--- 测试发送分析告警卡片 ---")
        # success = notifier.send_alert(
        #     session_id="SESSION_TEST_12345",
        #     reason="用户表达困惑",
        #     analysis_data=sample_analysis,
        #     staff_name="张三",
        #     customer_name="李四"
        # )
        # if success:
        #     print("测试卡片发送成功。")
        # else:
        #     print("测试卡片发送失败。")

        # 示例：测试发送普通文本消息
        # print("\n--- 测试发送普通文本消息 ---")
        # notifier.send_message("这是一条来自 FeishuBotNotifier 的测试文本消息。")

        # 示例：测试创建飞书任务
    #     print("\n--- 测试创建飞书任务 ---")
    #     task_response = notifier.create_feishu_task(
    #         summary="测试飞书任务",
    #         description="这是一个测试飞书任务。",
    #         session_id="SESSION_TEST_12345"
    #     )
    #     if task_response:
    #         print("飞书任务创建成功。")
    #     else:
    #         print("飞书任务创建失败。")
    #
    # else:
    #     print("请在 config.py 中配置 飞书应用 appid 以进行测试。")
