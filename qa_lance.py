#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
精简版相似问题聚类分析 - 专注于标准问题发现和精准度评估
"""

import os
import json
import time
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import re

# 机器学习库
try:
    from sklearn.cluster import KMeans
    from sklearn.metrics import silhouette_score, adjusted_rand_score
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 scikit-learn")
    SKLEARN_AVAILABLE = False

# 语义模型
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 sentence-transformers")
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# 数据库
try:
    import lancedb
    LANCEDB_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 lancedb")
    LANCEDB_AVAILABLE = False

import config
from store_data_to_sr import StarRocksStore


class StandardQuestionFinder:
    """标准问题发现器 - 精简版"""
    
    def __init__(self, model_path: str = "./paraphrase-multilingual-MiniLM-L12-v2"):
        """初始化标准问题发现器"""
        
        # 加载语义模型
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            print(f"[INFO] 加载语义模型: {model_path}")
            self.model = SentenceTransformer(model_path)
            print(f"[INFO] 模型维度: {self.model.get_sentence_embedding_dimension()}")
        else:
            raise ImportError("需要安装 sentence-transformers")
        
        # 数据库连接
        self.sr_store = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE
        )
        
        # LanceDB
        if LANCEDB_AVAILABLE:
            self.db = lancedb.connect("./standard_questions_db")
        
        # 统计信息
        self.results = {}
    
    def load_questions(self, limit: Optional[int] = None) -> pd.DataFrame:
        """加载相似问题数据"""
        try:
            if not self.sr_store.connect():
                raise Exception("无法连接到数据库")
            
            query = """
            SELECT 
                similar_question as question,
                catagory_1 as category,
                COUNT(*) as frequency
            FROM question_pairs
            WHERE similar_question IS NOT NULL 
            AND similar_question != ''
            AND LENGTH(TRIM(similar_question)) > 3
            GROUP BY similar_question, catagory_1
            ORDER BY frequency DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            print(f"[INFO] 加载问题数据...")
            self.sr_store.cursor.execute(query)
            results = self.sr_store.cursor.fetchall()
            
            columns = [desc[0] for desc in self.sr_store.cursor.description]
            df = pd.DataFrame(results, columns=columns)
            
            # 清理问题文本
            df['question_cleaned'] = df['question'].apply(self._clean_text)
            df = df[df['question_cleaned'].str.len() > 2].copy()
            
            print(f"[INFO] 加载了 {len(df)} 个问题")
            return df
            
        except Exception as e:
            print(f"[ERROR] 加载数据失败: {e}")
            return pd.DataFrame()
        finally:
            self.sr_store.disconnect()
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        text = str(text).strip()
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\u4e00-\u9fff\w\s\?\!。？！]', '', text)
        return text.strip()
    
    def cluster_questions(self, df: pd.DataFrame, similarity_threshold: float = 0.8) -> pd.DataFrame:
        """基于相似度阈值的精确聚类"""
        
        # 生成嵌入向量
        print(f"[INFO] 生成 {len(df)} 个问题的嵌入向量...")
        questions = df['question_cleaned'].tolist()
        embeddings = self.model.encode(questions, show_progress_bar=True)
        
        print(f"[INFO] 使用相似度阈值 {similarity_threshold} 进行精确聚类...")
        
        # 计算相似度矩阵
        similarity_matrix = cosine_similarity(embeddings)
        
        # 基于相似度阈值的聚类
        clusters = []
        used_indices = set()
        
        for i in range(len(embeddings)):
            if i in used_indices:
                continue
                
            # 找到所有与当前问题相似的问题
            similar_indices = []
            for j in range(len(embeddings)):
                if j not in used_indices and similarity_matrix[i][j] >= similarity_threshold:
                    similar_indices.append(j)
            
            # 如果找到相似的问题，形成一个聚类
            if len(similar_indices) >= 2:  # 至少2个问题才形成聚类
                # 验证聚类质量：聚类内任意两个问题的相似度都要超过阈值
                valid_cluster = True
                for idx1 in similar_indices:
                    for idx2 in similar_indices:
                        if idx1 != idx2 and similarity_matrix[idx1][idx2] < similarity_threshold * 0.9:  # 稍微宽松一点
                            valid_cluster = False
                            break
                    if not valid_cluster:
                        break
                
                if valid_cluster:
                    clusters.append(similar_indices)
                    used_indices.update(similar_indices)
                else:
                    # 如果聚类质量不够，尝试更严格的聚类
                    strict_cluster = [i]
                    for j in similar_indices:
                        if j != i:
                            # 检查与当前聚类中所有问题的相似度
                            min_sim = min(similarity_matrix[j][k] for k in strict_cluster)
                            if min_sim >= similarity_threshold:
                                strict_cluster.append(j)
                    
                    if len(strict_cluster) >= 2:
                        clusters.append(strict_cluster)
                        used_indices.update(strict_cluster)
        
        # 为剩余的单独问题分配独立聚类ID
        cluster_labels = [-1] * len(embeddings)
        cluster_id = 0
        
        for cluster_indices in clusters:
            for idx in cluster_indices:
                cluster_labels[idx] = cluster_id
            cluster_id += 1
        
        # 单独的问题分配独立ID
        for i in range(len(embeddings)):
            if cluster_labels[i] == -1:
                cluster_labels[i] = cluster_id
                cluster_id += 1
        
        # 添加结果到DataFrame
        df = df.copy()
        df['cluster_id'] = cluster_labels
        df['embedding'] = embeddings.tolist()
        
        # 计算聚类质量指标
        valid_clusters = [c for c in clusters if len(c) >= 2]
        if valid_clusters:
            # 计算聚类内平均相似度
            cluster_similarities = []
            for cluster_indices in valid_clusters:
                sims = []
                for i in cluster_indices:
                    for j in cluster_indices:
                        if i != j:
                            sims.append(similarity_matrix[i][j])
                if sims:
                    cluster_similarities.append(np.mean(sims))
            
            avg_similarity = np.mean(cluster_similarities)
        else:
            avg_similarity = 0.0
        
        print(f"[INFO] 精确聚类完成:")
        print(f"  - 总聚类数: {cluster_id}")
        print(f"  - 有效聚类数 (>=2个问题): {len(valid_clusters)}")
        print(f"  - 平均聚类内相似度: {avg_similarity:.4f}")
        print(f"  - 相似度阈值: {similarity_threshold}")
        
        self.results['clustering_quality'] = avg_similarity
        self.results['n_clusters'] = cluster_id
        self.results['valid_clusters'] = len(valid_clusters)
        self.results['similarity_threshold'] = similarity_threshold
        
        return df
    
    def find_standard_questions(self, df: pd.DataFrame) -> Dict[str, Any]:
        """找到每个聚类的标准问题 - 增强验证"""
        
        standard_questions = {}
        
        for cluster_id in sorted(df['cluster_id'].unique()):
            cluster_data = df[df['cluster_id'] == cluster_id].copy()
            
            # 只处理包含多个问题的聚类
            if len(cluster_data) < 2:
                continue
            
            # 验证聚类质量：计算聚类内相似度
            embeddings = np.array([emb for emb in cluster_data['embedding']])
            similarity_matrix = cosine_similarity(embeddings)
            
            # 计算聚类内最小相似度
            min_similarity = 1.0
            avg_similarity = 0.0
            count = 0
            
            for i in range(len(embeddings)):
                for j in range(i+1, len(embeddings)):
                    sim = similarity_matrix[i][j]
                    min_similarity = min(min_similarity, sim)
                    avg_similarity += sim
                    count += 1
            
            if count > 0:
                avg_similarity /= count
            
            # 如果聚类质量不够好，跳过
            if min_similarity < 0.7 or avg_similarity < 0.75:  # 更严格的阈值
                print(f"[WARN] 聚类 {cluster_id} 质量不够，跳过 (最小相似度: {min_similarity:.3f}, 平均相似度: {avg_similarity:.3f})")
                continue
            
            # 选择标准问题的策略
            candidates = {}
            
            # 1. 最高频率
            freq_best = cluster_data.loc[cluster_data['frequency'].idxmax()]
            candidates['frequency'] = {
                'question': freq_best['question_cleaned'],
                'score': freq_best['frequency'],
                'method': '最高频率'
            }
            
            # 2. 最接近聚类中心（到其他所有问题距离最小的问题）
            center_distances = []
            for i, emb1 in enumerate(embeddings):
                total_distance = 0
                for j, emb2 in enumerate(embeddings):
                    if i != j:
                        total_distance += 1 - cosine_similarity([emb1], [emb2])[0][0]  # 距离 = 1 - 相似度
                center_distances.append(total_distance)
            
            center_best_idx = np.argmin(center_distances)
            center_best = cluster_data.iloc[center_best_idx]
            candidates['centrality'] = {
                'question': center_best['question_cleaned'],
                'score': 1 / (center_distances[center_best_idx] + 0.001),
                'method': '最接近中心'
            }
            
            # 3. 最短问题（在相似度足够的前提下）
            # 找到与所有其他问题相似度都很高的最短问题
            valid_short_questions = []
            for i, (_, row) in enumerate(cluster_data.iterrows()):
                # 检查与其他所有问题的相似度
                min_sim_to_others = min(similarity_matrix[i][j] for j in range(len(embeddings)) if j != i)
                if min_sim_to_others >= 0.8:  # 与所有其他问题相似度都很高
                    valid_short_questions.append((row['question_cleaned'], len(row['question_cleaned']), row['frequency']))
            
            if valid_short_questions:
                # 按长度排序，选择最短的
                shortest = min(valid_short_questions, key=lambda x: x[1])
                candidates['shortest'] = {
                    'question': shortest[0],
                    'score': 1 / (shortest[1] + 1),
                    'method': '最短且高质量'
                }
            
            # 4. 综合评分（优先考虑质量和代表性）
            best_composite_score = -1
            best_composite_question = ""
            
            for i, (_, row) in enumerate(cluster_data.iterrows()):
                # 计算与其他问题的平均相似度（代表性）
                representativeness = np.mean([similarity_matrix[i][j] for j in range(len(embeddings)) if j != i])
                
                # 频率归一化
                freq_norm = row['frequency'] / cluster_data['frequency'].max()
                
                # 长度评分（越短越好，但不能太短）
                length_score = 1.0 if len(row['question_cleaned']) < 10 else 0.8
                
                # 综合评分：代表性权重最高
                composite_score = representativeness * 0.6 + freq_norm * 0.3 + length_score * 0.1
                
                if composite_score > best_composite_score:
                    best_composite_score = composite_score
                    best_composite_question = row['question_cleaned']
            
            candidates['composite'] = {
                'question': best_composite_question,
                'score': best_composite_score,
                'method': '综合评分'
            }
            
            # 选择综合评分最高的作为标准问题
            standard_question = candidates['composite']['question']
            
            # 收集聚类中的所有问题（已经过质量验证）
            all_questions = cluster_data['question_cleaned'].tolist()
            
            standard_questions[cluster_id] = {
                'standard_question': standard_question,
                'cluster_size': len(cluster_data),
                'total_frequency': cluster_data['frequency'].sum(),
                'avg_similarity': float(avg_similarity),
                'min_similarity': float(min_similarity),  # 添加最小相似度
                'all_questions': all_questions,
                'candidates': candidates,
                'quality_verified': True  # 标记为已验证质量
            }
        
        print(f"[INFO] 发现 {len(standard_questions)} 个高质量标准问题")
        return standard_questions
    
    def evaluate_precision(self, df: pd.DataFrame, standard_questions: Dict[str, Any]) -> Dict[str, float]:
        """评估标准问题的精准度"""
        
        precision_metrics = {}
        
        # 1. 聚类内相似度评估
        cluster_similarities = []
        for cluster_id, info in standard_questions.items():
            cluster_similarities.append(info['avg_similarity'])
        
        precision_metrics['avg_cluster_similarity'] = np.mean(cluster_similarities)
        precision_metrics['min_cluster_similarity'] = np.min(cluster_similarities)
        
        # 2. 标准问题代表性评估
        representativeness_scores = []
        
        for cluster_id, info in standard_questions.items():
            cluster_data = df[df['cluster_id'] == cluster_id]
            standard_q = info['standard_question']
            
            # 找到标准问题的向量
            std_embedding = None
            for _, row in cluster_data.iterrows():
                if row['question_cleaned'] == standard_q:
                    std_embedding = np.array(row['embedding'])
                    break
            
            if std_embedding is not None:
                # 计算标准问题与聚类中其他问题的平均相似度
                similarities = []
                for _, row in cluster_data.iterrows():
                    other_embedding = np.array(row['embedding'])
                    sim = cosine_similarity([std_embedding], [other_embedding])[0][0]
                    similarities.append(sim)
                
                representativeness_scores.append(np.mean(similarities))
        
        precision_metrics['avg_representativeness'] = np.mean(representativeness_scores)
        precision_metrics['min_representativeness'] = np.min(representativeness_scores)
        
        # 3. 聚类大小分布评估
        cluster_sizes = [info['cluster_size'] for info in standard_questions.values()]
        precision_metrics['avg_cluster_size'] = np.mean(cluster_sizes)
        precision_metrics['cluster_size_std'] = np.std(cluster_sizes)
        
        # 4. 综合精准度评分
        precision_metrics['overall_precision'] = (
            precision_metrics['avg_cluster_similarity'] * 0.4 +
            precision_metrics['avg_representativeness'] * 0.4 +
            (1 - precision_metrics['cluster_size_std'] / precision_metrics['avg_cluster_size']) * 0.2
        )
        
        return precision_metrics
    
    def save_to_lancedb(self, standard_questions: Dict[str, Any], table_name: str = "standard_questions"):
        """保存标准问题到LanceDB"""
        if not LANCEDB_AVAILABLE:
            print("[WARN] LanceDB 不可用，跳过保存")
            return
        
        try:
            data = []
            for cluster_id, info in standard_questions.items():
                data.append({
                    'cluster_id': int(cluster_id),
                    'standard_question': info['standard_question'],
                    'cluster_size': info['cluster_size'],
                    'total_frequency': info['total_frequency'],
                    'avg_similarity': info['avg_similarity'],
                    'all_questions': info['all_questions'],
                    'created_at': datetime.now()
                })
            
            # 删除旧表并创建新表
            if table_name in self.db.table_names():
                self.db.drop_table(table_name)
            
            self.db.create_table(table_name, data)
            print(f"[INFO] 标准问题已保存到 LanceDB: {table_name}")
            
        except Exception as e:
            print(f"[ERROR] 保存到LanceDB失败: {e}")
    
    def run_analysis(self, limit: Optional[int] = None, similarity_threshold: float = 0.8) -> Dict[str, Any]:
        """运行完整分析"""
        
        print("=" * 60)
        print("标准问题发现与精准度分析 (精确模式)")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. 加载数据
        print("\n[STEP 1] 加载问题数据...")
        df = self.load_questions(limit=limit)
        if df.empty:
            return {}
        
        # 2. 精确聚类
        print("\n[STEP 2] 执行精确聚类分析...")
        clustered_df = self.cluster_questions(df, similarity_threshold=similarity_threshold)
        
        # 3. 找标准问题
        print("\n[STEP 3] 发现高质量标准问题...")
        standard_questions = self.find_standard_questions(clustered_df)
        
        # 4. 评估精准度
        print("\n[STEP 4] 评估精准度...")
        precision_metrics = self.evaluate_precision(clustered_df, standard_questions)
        
        # 5. 保存结果
        print("\n[STEP 5] 保存结果...")
        self.save_to_lancedb(standard_questions)
        
        # 6. 输出报告
        duration = time.time() - start_time
        
        print(f"\n{'='*60}")
        print("精确分析完成报告")
        print(f"{'='*60}")
        print(f"处理时间: {duration:.2f} 秒")
        print(f"输入问题数: {len(df)}")
        print(f"相似度阈值: {similarity_threshold}")
        print(f"总聚类数: {self.results['n_clusters']}")
        print(f"有效聚类数: {self.results['valid_clusters']}")
        print(f"发现标准问题数: {len(standard_questions)}")
        print(f"聚类质量 (平均相似度): {self.results['clustering_quality']:.4f}")
        
        if len(standard_questions) > 0:
            print(f"\n精准度指标:")
            print(f"  平均聚类内相似度: {precision_metrics['avg_cluster_similarity']:.4f}")
            print(f"  平均代表性: {precision_metrics['avg_representativeness']:.4f}")
            print(f"  综合精准度评分: {precision_metrics['overall_precision']:.4f}")
            
            # 显示聚类质量分布
            min_similarities = [info['min_similarity'] for info in standard_questions.values()]
            avg_similarities = [info['avg_similarity'] for info in standard_questions.values()]
            
            print(f"\n聚类质量分布:")
            print(f"  最小相似度范围: {min(min_similarities):.3f} - {max(min_similarities):.3f}")
            print(f"  平均相似度范围: {min(avg_similarities):.3f} - {max(avg_similarities):.3f}")
            
            print(f"\n前10个高质量标准问题:")
            print("-" * 80)
            
            # 按质量排序显示（优先考虑最小相似度）
            sorted_standards = sorted(
                standard_questions.items(),
                key=lambda x: (x[1]['min_similarity'], x[1]['avg_similarity'], x[1]['cluster_size']),
                reverse=True
            )
            
            for i, (cluster_id, info) in enumerate(sorted_standards[:10], 1):
                print(f"{i:2d}. [{info['cluster_size']:2d}个问题] {info['standard_question']}")
                print(f"    最小相似度: {info['min_similarity']:.3f}, 平均相似度: {info['avg_similarity']:.3f}")
                print(f"    频次: {info['total_frequency']}")
                
                # 显示聚类中的其他问题（前3个）
                other_questions = [q for q in info['all_questions'] if q != info['standard_question']][:3]
                if other_questions:
                    print(f"    相似问题: {' | '.join(other_questions)}")
                print()
        else:
            print(f"\n[WARN] 未发现满足质量要求的标准问题")
            print(f"建议降低相似度阈值 (当前: {similarity_threshold})")
        
        # 保存详细结果 - 修复numpy类型问题
        results = {
            'standard_questions': {str(k): v for k, v in standard_questions.items()},
            'precision_metrics': precision_metrics,
            'clustering_quality': float(self.results['clustering_quality']),
            'similarity_threshold': similarity_threshold,
            'valid_clusters': self.results['valid_clusters'],
            'total_clusters': self.results['n_clusters'],
            'processing_time': duration
        }
        
        # 递归转换所有numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {str(k): convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, (np.integer, np.int32, np.int64)):
                return int(obj)
            elif isinstance(obj, (np.floating, np.float32, np.float64)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj
        
        results = convert_numpy_types(results)
        
        with open('standard_questions_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n[INFO] 详细结果已保存到: standard_questions_analysis.json")
        
        return results


def main():
    """主函数"""
    try:
        print("=" * 60)
        print("精确标准问题发现系统")
        print("=" * 60)
        print("说明：此系统优先考虑精确度，确保每个聚类内的问题真正相似")
        print(f"默认相似度阈值: 0.8 (越高越严格)")
        print()
        
        finder = StandardQuestionFinder()
        
        # 可以尝试不同的相似度阈值
        # 0.9 - 非常严格，只有高度相似的问题才会聚类
        # 0.8 - 严格 (推荐)
        # 0.7 - 相对宽松
        # 0.6 - 宽松
        
        results = finder.run_analysis(
            similarity_threshold=0.8  # 可以调节这个参数
        )
        
        # 如果结果不理想，可以尝试不同阈值
        if len(results.get('standard_questions', {})) < 10:
            print(f"\n[INFO] 发现的标准问题较少，尝试使用更宽松的阈值...")
            results = finder.run_analysis(
                limit=3000,
                similarity_threshold=0.75  # 稍微降低阈值
            )
        
    except Exception as e:
        print(f"[ERROR] 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 