#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import sys
import os
from qiyu_session_service import get_session_messages, format_session_messages

def save_result_to_file(data, output_file=None):
    """
    将结果保存到文件
    
    参数:
        data (dict): 要保存的数据
        output_file (str): 输出文件路径，如果为None则基于session_id生成
    
    返回:
        str: 保存文件的路径
    """
    if output_file is None:
        # 从数据中获取会话ID（如果可能）
        session_id = data.get("data", {}).get("sessionId", "unknown")
        import time
        timestamp = int(time.time())
        output_file = f"session_{session_id}_{timestamp}.json"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return output_file
    except Exception as e:
        print(f"保存文件错误: {e}", file=sys.stderr)
        return None

def main():
    parser = argparse.ArgumentParser(description="获取七鱼会话消息")
    parser.add_argument("session_id", type=int, help="要获取消息的会话ID")
    parser.add_argument("--mtypes", type=str, help="可选的消息类型过滤（以逗号分隔）")
    parser.add_argument("--timeout", type=int, default=30, help="API请求超时时间（秒），默认30秒")
    parser.add_argument("--output", "-o", type=str, help="输出结果到指定文件")
    parser.add_argument("--save", "-s", action="store_true", help="将结果保存到文件")
    parser.add_argument("--format", "-f", action="store_true", help="格式化输出会话消息")
    
    args = parser.parse_args()
    
    try:
        # 获取会话消息
        result = get_session_messages(args.session_id, args.mtypes, timeout=args.timeout)
        
        if result.get("code") == 200:
            print("获取会话消息成功!")
            
            # 格式化输出
            if args.format:
                formatted_messages = format_session_messages(result)
                print("\n" + "="*50 + " 格式化会话消息 " + "="*50)
                print(formatted_messages)
                print("="*120)
            else:
                # 打印结果
                print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 保存结果（如果需要）
            if args.save or args.output:
                output_path = save_result_to_file(result, args.output)
                if output_path:
                    print(f"结果已保存到文件: {output_path}")
        else:
            print(f"API错误: {result.get('message', '未知错误')}", file=sys.stderr)
            sys.exit(1)
            
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
