#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import argparse
from typing import Dict, List, Any
import glob
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

import config
from qa_similarity import QASimilarityManager

def load_qa_files(qa_dir: str) -> List[Dict[str, Any]]:
    """加载QA文件
    
    Args:
        qa_dir: QA文件目录
        
    Returns:
        QA数据列表
    """
    qa_files = glob.glob(os.path.join(qa_dir, "*.json"))
    qa_list = []
    
    print(f"找到 {len(qa_files)} 个QA文件")
    
    for qa_file in qa_files:
        try:
            with open(qa_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                qa_list.append(data)
        except Exception as e:
            print(f"加载文件 {qa_file} 失败: {e}")
            
    return qa_list

async def process_single_qa(qa_item: Dict[str, Any], 
                           similarity_manager: QASimilarity<PERSON>anager,
                           semaphore: asyncio.Semaphore) -> Dict[str, Any]:
    """处理单个QA数据
    
    Args:
        qa_item: QA数据项
        similarity_manager: 相似度管理器
        semaphore: 信号量，用于控制并发数
        
    Returns:
        处理结果
    """
    async with semaphore:  # 控制并发数
        try:
            standard_question = qa_item.get("标准问题", "")
            if not standard_question:
                return {"status": "skipped", "reason": "无标准问题"}
                
            # 直接使用完整的qa_item数据，包含所有需要的字段
            question_data = qa_item.copy()  # 使用原始数据的副本
            
            # 确保必要的字段存在（兼容性处理）
            if "推荐回复" not in question_data and "答案" in question_data:
                question_data["推荐回复"] = question_data["答案"]
            
            # 添加到相似度管理器
            result = await similarity_manager.add_question(question_data)
            
            # 检查是否找到相似问题
            if result.get("标准问题") == standard_question:
                return {
                    "status": "new_added",
                    "question": standard_question,
                    "result": result
                }
            else:
                return {
                    "status": "similar_found",
                    "question": standard_question,
                    "similar_to": result.get('标准问题', ''),
                    "result": result
                }
        
        except Exception as e:
            return {
                "status": "error",
                "question": qa_item.get("标准问题", ""),
                "error": str(e)
            }

async def process_qa_data_parallel(qa_list: List[Dict[str, Any]], 
                                 similarity_manager: QASimilarityManager,
                                 max_concurrent: int = 8,
                                 process_batch_size: int = 100) -> Dict[str, int]:
    """并行处理QA数据
    
    Args:
        qa_list: QA数据列表
        similarity_manager: 相似度管理器
        max_concurrent: 最大并发数
        process_batch_size: 每批处理的QA数据数量
        
    Returns:
        处理统计信息
    """
    stats = {
        "total": len(qa_list),
        "processed": 0,
        "similar_found": 0,
        "new_added": 0,
        "errors": 0,
        "skipped": 0
    }
    
    # 创建信号量控制并发数
    semaphore = asyncio.Semaphore(max_concurrent)
    
    print("=" * 80)
    print(f"开始并行处理 {len(qa_list)} 个QA数据")
    print(f"处理配置: 最大并发数={max_concurrent}, 每批数量={process_batch_size}")
    print("=" * 80)
    
    total_batches = (len(qa_list) + process_batch_size - 1) // process_batch_size
    
    for i in range(0, len(qa_list), process_batch_size):
        batch = qa_list[i:i + process_batch_size]
        current_batch = i // process_batch_size + 1
        
        print(f"\n处理批次 {current_batch}/{total_batches} (第 {i+1}-{min(i+len(batch), len(qa_list))} 个QA数据)")
        print("-" * 60)
        
        # 创建并行任务
        tasks = [
            process_single_qa(qa_item, similarity_manager, semaphore)
            for qa_item in batch
        ]
        
        # 并行执行当前批次
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        batch_stats = {"new": 0, "similar": 0, "error": 0, "skip": 0}
        
        for result in results:
            if isinstance(result, Exception):
                print(f"处理异常: {result}")
                stats["errors"] += 1
                batch_stats["error"] += 1
            elif isinstance(result, dict):
                status = result.get("status")
                if status == "new_added":
                    stats["new_added"] += 1
                    stats["processed"] += 1
                    batch_stats["new"] += 1
                elif status == "similar_found":
                    stats["similar_found"] += 1
                    stats["processed"] += 1
                    batch_stats["similar"] += 1
                elif status == "skipped":
                    stats["skipped"] += 1
                    batch_stats["skip"] += 1
                elif status == "error":
                    stats["errors"] += 1
                    batch_stats["error"] += 1
        
        # 打印批次统计
        print(f"批次 {current_batch} 完成: 新增={batch_stats['new']}, 归类={batch_stats['similar']}, 跳过={batch_stats['skip']}, 错误={batch_stats['error']}")
        
        # 每批处理完后，手动刷新一次批量数据
        print(f"正在刷新批量数据...")
        similarity_manager.flush_batch_data()
        
        # 短暂休息，避免过载
        await asyncio.sleep(0.1)
    
    # 打印最终统计
    print("\n" + "=" * 80)
    print("并行处理完成 - 最终统计:")
    print(f"   总数据量: {stats['total']}")
    print(f"   处理成功: {stats['processed']}")
    print(f"   新增标准问题: {stats['new_added']}")
    print(f"   归类到已有问题: {stats['similar_found']}")
    print(f"   跳过数据: {stats['skipped']}")
    print(f"   处理错误: {stats['errors']}")
    print("=" * 80)
    
    return stats

async def process_qa_data(qa_list: List[Dict[str, Any]], 
                   similarity_manager: QASimilarityManager) -> Dict[str, int]:
    """处理QA数据（保留原有的串行方法作为备选）
    
    Args:
        qa_list: QA数据列表
        similarity_manager: 相似度管理器
        
    Returns:
        处理统计信息
    """
    stats = {
        "total": len(qa_list),
        "processed": 0,
        "similar_found": 0,
        "new_added": 0,
        "errors": 0
    }
    
    print("=" * 80)
    print(f"开始串行处理 {len(qa_list)} 个QA数据")
    print("=" * 80)
    
    for i, qa_item in enumerate(qa_list, 1):
        try:
            standard_question = qa_item.get("标准问题", "")
            if not standard_question:
                print(f"[{i}/{len(qa_list)}] 跳过无标准问题的数据")
                continue
                
            print(f"\n[{i}/{len(qa_list)}] 处理问题: \"{standard_question}\"")
            
            # 直接使用完整的qa_item数据，包含所有需要的字段
            # 这样store_qa_to_sr方法可以访问session_id、item_index、问题相关会话记录等字段
            question_data = qa_item.copy()  # 使用原始数据的副本
            
            # 确保必要的字段存在（兼容性处理）
            if "推荐回复" not in question_data and "答案" in question_data:
                question_data["推荐回复"] = question_data["答案"]
            
            # 添加到相似度管理器
            result = await similarity_manager.add_question(question_data)
            
            # 更新统计信息
            stats["processed"] += 1
            
            # 检查是否找到相似问题
            if result.get("标准问题") == standard_question:
                stats["new_added"] += 1
                print(f"[{i}/{len(qa_list)}] 新问题添加成功")
            else:
                stats["similar_found"] += 1
                print(f"[{i}/{len(qa_list)}] 归类到已有问题: \"{result.get('标准问题', '')}\"")
        
        except Exception as e:
            print(f"[{i}/{len(qa_list)}] 处理QA数据失败: {e}")
            stats["errors"] += 1
    
    # 打印最终统计
    print("\n" + "=" * 80)
    print("串行处理完成 - 最终统计:")
    print(f"   总数据量: {stats['total']}")
    print(f"   处理成功: {stats['processed']}")
    print(f"   新增标准问题: {stats['new_added']}")
    print(f"   归类到已有问题: {stats['similar_found']}")
    print(f"   处理错误: {stats['errors']}")
    print("=" * 80)
            
    return stats

async def process_single_qa_concurrent(qa_item: Dict[str, Any], 
                                   similarity_manager: QASimilarityManager,
                                   semaphore: asyncio.Semaphore) -> Dict[str, Any]:
    """并发模式处理单个QA数据（无锁版本）
    
    Args:
        qa_item: QA数据项
        similarity_manager: 相似度管理器
        semaphore: 信号量，用于控制并发数
        
    Returns:
        处理结果
    """
    async with semaphore:  # 控制并发数
        try:
            standard_question = qa_item.get("标准问题", "")
            if not standard_question:
                return {"status": "skipped", "reason": "无标准问题"}
                
            # 使用并发版本的add_question方法
            question_data = qa_item.copy()
            
            # 确保必要的字段存在
            if "推荐回复" not in question_data and "答案" in question_data:
                question_data["推荐回复"] = question_data["答案"]
            
            # 调用并发版本
            result = await similarity_manager.add_question_concurrent(question_data)
            
            # 检查是否找到相似问题
            if result.get("标准问题") == standard_question:
                return {
                    "status": "new_added",
                    "question": standard_question,
                    "result": result
                }
            else:
                return {
                    "status": "similar_found",
                    "question": standard_question,
                    "similar_to": result.get('标准问题', ''),
                    "result": result
                }
        
        except Exception as e:
            return {
                "status": "error",
                "question": qa_item.get("标准问题", ""),
                "error": str(e)
            }

async def process_qa_data_concurrent(qa_list: List[Dict[str, Any]], 
                                   similarity_manager: QASimilarityManager,
                                   max_concurrent: int = 50,
                                   process_batch_size: int = 200) -> Dict[str, int]:
    """高并发处理QA数据（允许重复，后续修复）
    
    Args:
        qa_list: QA数据列表
        similarity_manager: 相似度管理器
        max_concurrent: 最大并发数（建议50-100）
        process_batch_size: 每批处理的QA数据数量
        
    Returns:
        处理统计信息
    """
    stats = {
        "total": len(qa_list),
        "processed": 0,
        "similar_found": 0,
        "new_added": 0,
        "errors": 0,
        "skipped": 0
    }
    
    # 启用并发模式
    similarity_manager.enable_concurrent_mode()
    
    # 创建信号量控制并发数
    semaphore = asyncio.Semaphore(max_concurrent)
    
    print("🚀" * 30)
    print(f"🚀 高并发模式启动")
    print(f"🚀 数据量: {len(qa_list)} 个QA对")
    print(f"🚀 最大并发数: {max_concurrent}")
    print(f"🚀 批量大小: {process_batch_size}")
    print(f"🚀 注意: 此模式允许重复问题，请使用修复工具优化")
    print("🚀" * 30)
    
    total_batches = (len(qa_list) + process_batch_size - 1) // process_batch_size
    
    for i in range(0, len(qa_list), process_batch_size):
        batch = qa_list[i:i + process_batch_size]
        current_batch = i // process_batch_size + 1
        
        print(f"\n🚀 处理批次 {current_batch}/{total_batches} (第 {i+1}-{min(i+len(batch), len(qa_list))} 个QA数据)")
        print(f"🚀 批次并发数: {min(max_concurrent, len(batch))}")
        print("-" * 60)
        
        batch_start_time = time.time()
        
        # 创建并行任务
        tasks = [
            process_single_qa_concurrent(qa_item, similarity_manager, semaphore)
            for qa_item in batch
        ]
        
        # 高并发执行当前批次
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        batch_end_time = time.time()
        batch_time = batch_end_time - batch_start_time
        
        # 统计结果
        batch_stats = {"new": 0, "similar": 0, "error": 0, "skip": 0}
        
        for result in results:
            if isinstance(result, Exception):
                print(f"处理异常: {result}")
                stats["errors"] += 1
                batch_stats["error"] += 1
            elif isinstance(result, dict):
                status = result.get("status")
                if status == "new_added":
                    stats["new_added"] += 1
                    stats["processed"] += 1
                    batch_stats["new"] += 1
                elif status == "similar_found":
                    stats["similar_found"] += 1
                    stats["processed"] += 1
                    batch_stats["similar"] += 1
                elif status == "skipped":
                    stats["skipped"] += 1
                    batch_stats["skip"] += 1
                elif status == "error":
                    stats["errors"] += 1
                    batch_stats["error"] += 1
        
        # 计算性能指标
        throughput = len(batch) / batch_time if batch_time > 0 else 0
        
        # 打印批次统计
        print(f"🚀 批次 {current_batch} 完成: 新增={batch_stats['new']}, 归类={batch_stats['similar']}, 跳过={batch_stats['skip']}, 错误={batch_stats['error']}")
        print(f"🚀 批次耗时: {batch_time:.2f}秒, 吞吐量: {throughput:.1f} QA/秒")
        
        # 每批处理完后，手动刷新一次批量数据
        print(f"🚀 正在刷新批量数据...")
        similarity_manager.flush_batch_data()
        
        # 短暂休息，避免过载
        await asyncio.sleep(0.05)  # 更短的休息时间
    
    # 打印最终统计
    print("\n" + "🚀" * 30)
    print("🚀 高并发处理完成 - 最终统计:")
    print(f"🚀   总数据量: {stats['total']}")
    print(f"🚀   处理成功: {stats['processed']}")
    print(f"🚀   新增标准问题: {stats['new_added']}")
    print(f"🚀   归类到已有问题: {stats['similar_found']}")
    print(f"🚀   跳过数据: {stats['skipped']}")
    print(f"🚀   处理错误: {stats['errors']}")
    print("🚀" * 30)
    print("⚠️  注意: 高并发模式可能产生重复问题，请运行修复工具:")
    print("⚠️  python qa_repair_tool.py --threshold 0.85")
    print("🚀" * 30)
    
    return stats

async def main():
    parser = argparse.ArgumentParser(description="将QA数据标准化并存入Qdrant")
    parser.add_argument("--qa_dir", type=str, required=True, help="QA文件目录")
    parser.add_argument("--threshold", type=float, default=config.QA_SIMILARITY_THRESHOLD, 
                        help=f"相似度阈值，默认为{config.QA_SIMILARITY_THRESHOLD}")
    parser.add_argument("--fallback_threshold", type=float, default=0.6, 
                        help="兜底阈值，默认为0.6")
    parser.add_argument("--parallel", action="store_true", help="使用并行处理模式")
    parser.add_argument("--concurrent", action="store_true", help="使用高并发处理模式（允许重复，后续修复）")
    parser.add_argument("--max_concurrent", type=int, default=10, help="最大并发数，默认为10")
    parser.add_argument("--db_batch_size", type=int, default=100, help="StarRocks数据库批量写入大小，默认为100")
    parser.add_argument("--process_batch_size", type=int, default=100, help="并行处理时每批处理的QA数据数量，默认为100")
    parser.add_argument("--use_combined_encoding", action="store_true", help="使用组合内容编码（问题主导+答案辅助），否则只使用问题编码")
    args = parser.parse_args()
    
    # 处理模式选择
    if args.concurrent and args.parallel:
        print("错误：不能同时使用 --concurrent 和 --parallel 模式")
        return
    
    processing_mode = "高并发" if args.concurrent else ("并行" if args.parallel else "串行")
    
    print(f"开始处理QA数据目录: {args.qa_dir}")
    print(f"主相似度阈值: {args.threshold}")
    print(f"兜底相似度阈值: {args.fallback_threshold}")
    print(f"处理模式: {processing_mode}")
    print(f"StarRocks批量写入大小: {args.db_batch_size}")
    print(f"处理批量大小: {args.process_batch_size}")
    print(f"编码模式: {'组合内容编码（问题主导）' if args.use_combined_encoding else '问题编码'}")
    
    if args.concurrent:
        # 高并发模式的特殊配置
        if args.max_concurrent < 20:
            args.max_concurrent = 50  # 自动调整为更高的并发数
            print(f"🚀 高并发模式：自动调整并发数为 {args.max_concurrent}")
        if args.process_batch_size < 100:
            args.process_batch_size = 200  # 自动调整批量大小
            print(f"🚀 高并发模式：自动调整批量大小为 {args.process_batch_size}")
        print(f"🚀 高并发模式：最大并发数 {args.max_concurrent}")
    elif args.parallel:
        print(f"最大并发数: {args.max_concurrent}")
    
    # 加载QA文件
    qa_list = load_qa_files(args.qa_dir)
    if not qa_list:
        print("未找到有效的QA数据，退出")
        return
        
    # 初始化相似度管理器
    similarity_manager = QASimilarityManager(
        similarity_threshold=args.threshold,
        batch_size=args.db_batch_size,
        use_combined_encoding=args.use_combined_encoding,
        fallback_threshold=args.fallback_threshold
    )
    
    try:
        # 根据模式选择处理方法
        if args.concurrent:
            # 高并发模式
            stats = await process_qa_data_concurrent(
                qa_list, 
                similarity_manager, 
                args.max_concurrent, 
                args.process_batch_size
            )
        elif args.parallel:
            # 普通并行模式
            stats = await process_qa_data_parallel(
                qa_list, 
                similarity_manager, 
                args.max_concurrent, 
                args.process_batch_size
            )
        else:
            # 串行模式
            stats = await process_qa_data(qa_list, similarity_manager)
        
        # 手动刷新所有批量数据，确保数据及时入库
        print("处理完成，正在刷新批量数据...")
        similarity_manager.flush_batch_data()
        
        # 输出统计信息
        print("\n处理统计:")
        print(f"总数据量: {stats['total']}")
        print(f"处理成功: {stats['processed']}")
        print(f"找到相似问题: {stats['similar_found']}")
        print(f"添加新问题: {stats['new_added']}")
        print(f"处理错误: {stats['errors']}")
        if 'skipped' in stats:
            print(f"跳过数据: {stats['skipped']}")
        
        # 如果是高并发模式，提示运行修复工具
        if args.concurrent:
            print("\n🔧 建议接下来运行修复工具优化重复问题:")
            print("🔧 python qa_repair_tool.py --threshold 0.85 --dry_run  # 先预览")
            print("🔧 python qa_repair_tool.py --threshold 0.85            # 执行修复")
        
    finally:
        # 关闭连接
        similarity_manager.close()
        
if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 