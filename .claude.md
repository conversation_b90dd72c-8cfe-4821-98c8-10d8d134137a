# VOX Inspect - AI客服质量监控系统

## 项目概述

VOX Inspect 是一个智能客服质量监控系统，通过定时从七鱼客服系统拉取会话数据，使用AI进行智能分析，并实时推送告警到Slack。系统支持客户历史问题分析、会话质量评估等功能。

## 核心功能

### 1. 自动数据采集与分析
- **定时任务**：每小时自动从七鱼API拉取客服会话数据
- **数据处理**：自动解密、解压和处理七鱼导出的会话数据
- **AI分析**：使用内部LLM服务（DeepSeek-R1）进行智能分析
- **数据存储**：分析结果存储到StarRocks数据库

### 2. 实时告警系统
- **Slack集成**：支持Block消息和交互按钮
- **分级告警**：一级（红色）、二级（橙色）、三级（蓝色）
- **告警内容**：
  - 用户核心诉求及佐证
  - 会话基本信息（时长、轮次、效率）
  - 风险点识别（投诉风险、类型、渠道）

### 3. Slack机器人功能
- **客户历史分析**：输入客户ID查看历史问题分析
- **会话详情查询**：点击按钮查看完整会话记录
- **帮助命令**：输入"帮助"获取使用说明

### 4. LLM服务模块
- **统一接口**：`LLMService`类提供标准化的AI调用接口
- **多种分析能力**：
  - 客户历史问题分析
  - 文本情感分析
  - 会话内容总结
  - 会话质量评估
- **错误处理**：完善的异常处理和日志记录

## 技术架构

### 系统组件
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   七鱼客服系统   │────>│   VOX Inspect   │────>│  Slack 告警     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │
                               ├── LLM服务 (DeepSeek-R1)
                               ├── StarRocks数据库
                               └── Prompt Service API
```

### 核心模块

1. **main.py**: 主程序入口，定时任务调度
2. **qiyu_api.py**: 七鱼API客户端，负责数据导出和下载
3. **processor.py**: 数据处理逻辑，解析会话和消息数据
4. **llm_service.py**: LLM服务封装，提供AI分析能力
5. **notify_bot.py**: Slack通知和机器人交互处理
6. **store_data_to_sr.py**: StarRocks数据库操作
7. **config.py**: 配置管理，支持环境变量

## 数据流程

1. **数据采集**：定时从七鱼API导出指定时间段的会话数据
2. **数据下载**：轮询检查导出状态，下载加密的zip文件
3. **数据解密**：使用QIYU_ENCRYPT_KEY解密下载的文件
4. **数据解析**：解析session.txt和message.txt文件
5. **AI分析**：调用LLM服务和Prompt Service进行智能分析
6. **结果存储**：将分析结果存储到StarRocks数据库
7. **告警推送**：根据分析结果推送Slack告警

## 环境配置

### 必需的环境变量
```bash
# 七鱼API配置
QIYU_APPKEY=your_qiyu_appkey
QIYU_ENCRYPT_KEY=your_encrypt_key

# LLM服务配置
LLM_SERVICE_URL=your_llm_service_url
LLM_SERVICE_TIMEOUT=50

# Prompt Service配置
PROMPT_SERVICE_ID=139
PROMPT_SERVICE_API_URL=https://fastflow.longbridge-inc.com/apps/node/prompts_service

# StarRocks数据库配置
STARROCKS_HOST=your_starrocks_host
STARROCKS_PORT=9030
STARROCKS_USER=your_db_user
STARROCKS_PASSWORD=your_db_password
STARROCKS_DATABASE=your_database

# Slack配置
SLACK_ENABLED=true
SLACK_WEBHOOK_URL=your_webhook_url
SLACK_CHANNEL=your_channel
SLACK_BOT_TOKEN=your_bot_token
SLACK_SIGNING_SECRET=your_signing_secret
```

### 可选配置
```bash
# 并发处理
MAX_CONCURRENT_THREADS=8

# 任务检查器
CHECKER_INTERVAL=300
TASK_REMINDER_THRESHOLD=4320
CHECKER_DEFAULT_TIME=10:00

# 轮询配置
POLL_INTERVAL_SECONDS=10
MAX_POLL_ATTEMPTS=30
```

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t vox-inspect .

# 运行容器
docker run -d \
  --name vox-inspect \
  --env-file deploy/prod.env \
  vox-inspect
```

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export $(cat deploy/test.env | xargs)

# 运行程序
python main.py
```

## 数据库表结构

### user_complaints表
- session_id: 会话ID
- user_demand: 用户核心诉求
- demand_evidence: 诉求佐证
- start_time: 开始时间
- member_id: 会员ID（通过session_id关联）

### conversations表
- session_id: 会话ID
- member_id: 会员ID
- conversation_text: 会话内容
- staff_name: 客服名称
- created_time: 创建时间

## 最近更新

基于最近的提交记录，系统进行了以下优化：

1. **格式化优化**：精简了分析事件的返回字段，优化了内容格式
2. **配置优化**：优化了环境变量配置管理
3. **LLM集成**：新增了LLM服务配置，支持更强大的AI分析
4. **历史分析**：增加了客户历史记录分析功能
5. **单例优化**：优化了通知器的单例实现
6. **UI改进**：使用Slack Block消息格式，提升用户体验

## 注意事项

1. **安全性**：请勿将API密钥等敏感信息提交到版本控制
2. **性能**：建议根据数据量调整并发线程数
3. **监控**：定期检查日志，确保系统正常运行
4. **备份**：定期备份StarRocks数据库中的分析结果

## 维护指南

1. **日志查看**：检查程序输出的DEBUG和ERROR日志
2. **数据验证**：定期验证七鱼数据导出是否正常
3. **告警测试**：定期测试Slack告警是否正常发送
4. **性能优化**：根据实际负载调整并发参数

## 联系方式

如有问题或建议，请联系系统维护团队。