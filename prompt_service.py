import requests
import json
import config # 确保导入 config

class PromptServiceAPI:
    def __init__(self, api_url):
        self.api_url = api_url
        self.headers = {
            "Content-Type": "application/json"
        }

    def send_prompt(self, payload):
        """
        向 Prompt Service 发送请求。

        Args:
            payload (dict): 请求体数据。

        Returns:
            dict or None: 如果请求成功，返回 JSON 响应；否则返回 None。
        """
        try:
            # 使用 config 中的超时设置
            # timeout=(连接超时时间, 读取超时时间)
            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=(10, config.PROMPT_SERVICE_READ_TIMEOUT)
            )
            response.raise_for_status()  # 如果状态码不是 2xx，则抛出 HTTPError
            return response.json()
        except requests.exceptions.Timeout as e:
            print(f"[ERROR] 请求 Prompt Service API 时发生超时错误: {e}")
            return None
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 请求 Prompt Service API 时发生错误: {e}")
            # 可以在这里添加更详细的错误日志，例如打印响应内容（如果存在）
            if e.response is not None:
                print(f"Response status code: {e.response.status_code}")
                print(f"Response text: {e.response.text}")
            return None
        except json.JSONDecodeError as e:
            print(f"[ERROR] 解析 Prompt Service API 响应 JSON 时失败: {e}")
            # 打印原始响应文本以帮助调试
            try:
                print(f"原始响应文本: {response.text}")
            except NameError: # 如果 response 对象在 json.loads 之前就出错了
                 print("无法获取原始响应文本。")
            return None
        except Exception as e:
            print(f"[ERROR] 调用 Prompt Service API 时发生未知错误: {e}")
            return None
