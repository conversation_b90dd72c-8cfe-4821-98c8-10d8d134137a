# vox-inspect

一个 Python 项目，用于定时从七鱼客服系统拉取会话数据，处理后发送到 Prompt Service API。

## 功能

-   每小时（可配置）自动执行。
-   通过七鱼 Open API 请求导出指定时间段的会话数据。
-   自动轮询检查导出状态并下载导出的数据（加密 zip 文件）。
-   使用 App Key 解压下载的文件。
-   处理解压后的数据（具体处理逻辑需在 `processor.py` 中实现）。
-   将处理后的数据发送到配置的 Prompt Service API。

## 结构

```
.vox-inspect/
├── .venv/                   # (可选) 虚拟环境
├── downloads/               # 下载的七鱼 zip 文件存放目录
├── extracted_data/          # 解压后的数据存放目录
├── config.py                # 配置文件 (API Keys, URLs)
├── main.py                  # 主程序入口，包含调度逻辑
├── processor.py             # 数据处理逻辑
├── prompt_service.py        # Prompt Service API 客户端
├── qiyu_api.py              # 七鱼 Open API 客户端
├── requirements.txt         # Python 依赖包
└── README.md                # 项目说明
```

## 配置

1.  **安装依赖:**
    ```bash
    pip install -r requirements.txt
    ```
2.  **配置 API Keys 和 URL:**
    编辑 `config.py` 文件，填入您的七鱼 `APPKEY`、`ENCRYPT_KEY` 以及 Prompt Service 的 `API_URL`。
    ```python
    # config.py
    QIYU_APPKEY = "YOUR_QIYU_APPKEY"
    QIYU_ENCRYPT_KEY = "YOUR_QIYU_ENCRYPT_KEY"
    PROMPT_SERVICE_API_URL = "YOUR_PROMPT_SERVICE_API_URL"
    DOWNLOAD_DIR = "./downloads"
    EXTRACT_DIR = "./extracted_data"
    ```
    **注意:** 出于安全考虑，强烈建议不要将敏感的 Keys 直接硬编码在代码中或提交到版本控制系统。考虑使用环境变量或其他密钥管理方案。

## 实现细节

-   **数据处理:** 您需要根据七鱼导出的实际数据格式（通常是 `session.txt` 和 `message.txt` 等文件，可能是 JSON Lines 或其他格式）在 `processor.py` 中实现 `process_extracted_data` 函数。此函数负责解析文件内容，提取所需信息，并调用 `format_prompt_payload`（也需要您实现）来构建发送到 Prompt Service 的请求体。
-   **时间范围:** `main.py` 中的 `job` 函数目前使用 `qiyu_api.get_dt_content` 来获取前一天的数据。如果您需要按小时拉取，请修改此部分逻辑（参考代码中的注释），并确认七鱼 API 是否支持按小时精确导出。
-   **调度:** `main.py` 默认使用 `schedule.every(1).minutes.do(job)` 进行测试。请根据需要修改为 `schedule.every().hour.do(job)` 或其他频率。

## 运行

```bash
python main.py
```

程序启动后会根据设定的计划（默认为每分钟）执行任务。
