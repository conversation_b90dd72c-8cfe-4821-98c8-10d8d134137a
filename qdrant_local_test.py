# 导入所需库
from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams, PointStruct
import numpy as np
import time
import json
# 1. 测试数据准备，去 /Users/<USER>/Longbridge/vox-inspect/qa_results/2025-05-01_to_2025-05-02/qa_pairs 中获取
qa_list = []
with open("/Users/<USER>/Longbridge/vox-inspect/qa_results/2025-05-01_to_2025-05-02/qa_pairs", "r") as f:
    for line in f:
        qa_list.append(json.loads(line))

# 2. 文本向量化模型
model = SentenceTransformer('paraphrase-MiniLM-L6-v2')

# 3. 连接 Qdrant 服务（假设本地已启动 Qdrant，端口 6333）
client = QdrantClient(host="localhost", port=6333)

# 4. 创建集合（如果已存在则重建）
collection_name = "qa_test"
vector_size = model.get_sentence_embedding_dimension()

try:
    client.delete_collection(collection_name=collection_name)
except Exception:
    pass

client.create_collection(
    collection_name=collection_name,
    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
)

# 5. 插入第一个问题
"""
{
  "session_id": 11758169795,
  "item_index": 1,
  "staff_name": "俞淑婷",
  "customer_name": "新用户_5rx3yN",
  "member_id": 0,
  "标准问题": "如何下载iOS测试版客户端？",
  "问题相关会话记录": [
    "iOS有测试版的客户端连接吗？麻烦可以给我一个吗",
    "您可以通过该链接查看下载的"
  ],
  "答案": "您可以通过该链接查看下载的。",
  "答案相关会话记录": [
    "好的，请稍等",
    "https://support.longbridgehk.com/topics/28nggs8/rgcbgu 您可以通过该链接查看下载的"
  ],
  "一级分类": "产品功能相关",
  "二级分类": "APP相关",
  "三级分类": "如何下载及下载链接",
  "processed_time": "2025-05-21 16:05:06"
}
"""
first = qa_list[0]
vec = model.encode([first["标准问题"]])[0]
point_id = int(time.time() * 1000)
client.upsert(
    collection_name=collection_name,
    points=[PointStruct(id=point_id, vector=vec.tolist(), payload={
        "标准问题": first["标准问题"],
        "一级分类": first["一级分类"],
        "推荐回复": first["答案"],
        "相似问题": [first["标准问题"]]
    })]
)
print(f"插入首条标准问题: {first['标准问题']}")

# 6. 依次处理后续问题，模拟同义问题追加和新问题写入
SIMILARITY_THRESHOLD = 0.85
for qa in qa_list[1:]:
    q_vec = model.encode([qa["标准问题"]])[0]
    result = client.search(collection_name=collection_name, query_vector=q_vec.tolist(), limit=1)
    if result and result[0].score >= SIMILARITY_THRESHOLD:
        old_point = result[0]
        old_payload = old_point.payload
        print(f"找到同义问题: '{qa['标准问题']}' ≈ '{old_payload['标准问题']}' (score={result[0].score:.3f})")
        # 如果新问题不在"相似问题"数组中，则追加
        if qa["标准问题"] not in old_payload["相似问题"]:
            new_similar = old_payload["相似问题"] + [qa["标准问题"]]
            client.set_payload(collection_name=collection_name, payload={"相似问题": new_similar}, points=[old_point.id])
            print(f"已追加到相似问题列表: {new_similar}")
    else:
        # 不是同义问题，写入新问题
        new_id = int(time.time() * 1000)
        client.upsert(
            collection_name=collection_name,
            points=[PointStruct(id=new_id, vector=q_vec.tolist(), payload={
                "标准问题": qa["标准问题"],
                "一级分类": qa["一级分类"],
                "推荐回复": qa["推荐回复"],
                "相似问题": [qa["标准问题"]]
            })]
        )
        print(f"新标准问题已插入: {qa['标准问题']}")

# 7. 查询集合内容，验证写入和相似问题归类
result = client.scroll(collection_name=collection_name, limit=20)
print("\n集合内容预览：")
for point in result[0]:
    print(point)

