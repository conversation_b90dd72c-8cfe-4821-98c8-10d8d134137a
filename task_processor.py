import time
import datetime
from typing import List, Dict, Any
from store_data_to_sr import StarRocksStore
from feishu_bot import FeishuBotNotifier
import config

class TaskProcessor:
    """处理告警任务和检查任务完成情况的类"""
    
    def __init__(self, starrocks_store: StarRocksStore, feishu_notifier: FeishuBotNotifier):
        """初始化任务处理器
        
        Args:
            starrocks_store (StarRocksStore): StarRocks数据库连接
            feishu_notifier (FeishuBotNotifier): 飞书通知器
        """
        self.store = starrocks_store
        self.notifier = feishu_notifier
            
    def check_pending_tasks(self):
        """检查待处理的任务，发送提醒"""
        try:            
            # 获取所有超时待处理任务
            overdue_tasks = self.store.get_all_pending_tasks()
            if not overdue_tasks:
                print("[INFO] 没有超时未处理任务")
                return
                
            print(f"[INFO] 发现 {len(overdue_tasks)} 个超时未处理任务")
            
            # 构建提醒消息
            title = f"当前有 {len(overdue_tasks)} 个任务超过 {config.TASK_REMINDER_THRESHOLD/60} 小时未处理"
            
            # 使用富文本组件构建表格
            table_content = "| 会话ID | 责任人 | 预警类型 | 告警时间 | 预期完成时间 | 当前状态 |\n"
            table_content += "| ------ | ------ | ------ | ------ | ------ | ------ |\n"
            
            for task in overdue_tasks:
                # 计算预期完成时间（创建时间+阈值时间）
                create_time = task['create_time']
                expected_time = create_time + datetime.timedelta(minutes=config.TASK_REMINDER_THRESHOLD)
                expected_time_str = expected_time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 构建表格行，使用颜色标记状态并@提及责任人
                table_content += f"| {task['session_id']} | <person id='{task['assigned_open_id']}' show_name=true></person> | {task['alert_type']} | {task['create_time']} | "
                table_content += f"{expected_time_str} | {task['task_status']} |\n"
                # 添加@提及责任人
                table_content += f"<at id='{task['assigned_open_id']}'></at>"
            
            # 发送提醒
            self.notifier.send_card({
                "schema": "2.0",
                "config": {"wide_screen_mode": True},
                "header": {
                    "title": {"content": "超时待处理任务提醒", "tag": "plain_text"},
                    "template": "red"  # 使用红色模板表示紧急
                },
                "body": {
                    "elements": [
                        {
                            "tag": "markdown",
                            "content": f"**{title}**\n\n{table_content}",
                            "text_size": "normal",
                            "text_align": "left"
                        },
                        {
                            "tag": "hr"
                        },
                        {
                            "tag": "markdown",
                            "content": "**提示：** 请各任务分配人尽快完成超时未处理任务，请负责人跟踪进度",
                            "text_align": "left"
                        }
                    ]
                }
            })
            
        except Exception as e:
            print(f"[ERROR] 检查值班人员的任务时出错: {e}")
    
    def run_checker(self):
        """运行检查器，持续检查告警和任务状态"""
        print(f"[INFO] 启动任务检查器 @ {datetime.datetime.now()}")
        
        try:
            while True:
                try:
                    # 检查待处理任务
                    self.check_pending_tasks()
                except Exception as e:
                    print(f"[ERROR] 检查周期中发生错误: {e}")
                
                # 暂停一段时间再检查
                print(f"[INFO] 检查完成，等待 {config.CHECKER_INTERVAL} 秒后再次检查")
                time.sleep(config.CHECKER_INTERVAL)
                
        except KeyboardInterrupt:
            print("[INFO] 收到中断信号，任务检查器停止")
        except Exception as e:
            print(f"[ERROR] 任务检查器运行出错: {e}")
        finally:
            # 断开数据库连接
            if self.store:
                self.store.disconnect() 
    
    def run_once(self):
        """只运行一次检查，而不是持续循环"""
        print(f"[INFO] 执行一次任务检查 @ {datetime.datetime.now()}")
        
        try:
            # 检查待处理任务
            self.check_pending_tasks()
            print("[INFO] 单次任务检查完成")
        except Exception as e:
            print(f"[ERROR] 检查任务时出错: {e}")
        finally:
            # 断开数据库连接
            if self.store:
                self.store.disconnect() 