import os
import json
import glob
import re
import datetime
import html
import time
import threading
import queue
from queue import Empty
from collections import defaultdict
from prompt_service import PromptServiceAPI
import config
from response_proc import ResponseProcessor
from feishu_bot import FeishuBotNotifier
from store_data_to_sr import StarRocksStore
from utils import parse_time, format_timestamp

# 消息类型描述映射
MESSAGE_TYPE_DESC = {
    0: "系统消息",
    1: "文本消息",
    2: "图片消息",
    3: "语音消息",
    4: "文件消息",
    5: "视频消息",
    6: "系统提示消息",
    100: "自定义消息",
    110: "机器人答案",
    111: "机器人答案反馈",
    112: "超时关闭前提醒",
    113: "超时关闭提醒",
    114: "工单流程消息",
    115: "富文本消息",
    116: "敏感词屏蔽消息",
    117: "客服拒绝转接",
    118: "客服提交工单消息",
    119: "客服发送邀请评价",
    120: "访客参评",
    121: "主企业客服转接会话到子企业信息",
    122: "客服邀请评价详情",
    123: "emoji消息",
    124: "客服转接到主企业"
}

EMOTION_DESC = {
    0: "满意",
    1: "正常",
    2: "焦虑",
    3: "不满",
    4: "愤怒"
}

STRENGTH_DESC = {
    0: "未知",
    1: "轻微",
    2: "中度",
    3: "重度"
}

# 消息评价描述映射
EVALUATION_DESC = {
    0: "不显示评价",
    1: "未评价",
    2: "有用",
    3: "没用"
}

# 匹配类型描述映射
MATCH_TYPE_DESC = {
    0: "未匹配",
    1: "精确匹配",
    2: "相似匹配"
}

def get_message_type_desc(mType):
    """获取消息类型的描述文本"""
    return MESSAGE_TYPE_DESC.get(mType, f"未知消息类型({mType})")

# 新增：工作线程函数
def worker_thread(task_queue, prompt_service, response_processor, shared_counters, lock, session_metadata):
    """
    工作线程，从队列获取任务，发送到 Prompt Service 并处理响应。

    Args:
        task_queue (queue.Queue): 任务队列，包含 (session_id, prompt_payload) 元组。
        prompt_service (PromptServiceAPI): Prompt Service API 客户端实例。
        response_processor (ResponseProcessor): 响应处理器实例。
        shared_counters (dict): 包含需要线程安全更新的计数器的字典。
        lock (threading.Lock): 用于保护共享计数器的锁。
        session_metadata (dict): 包含会话元数据的字典。
    """
    while True:
        try:
            # 从队列获取任务，设置超时以允许线程在队列为空时退出
            session_id, prompt_payload = task_queue.get(timeout=1)
        except Empty:
            # print(f"[DEBUG] 线程 {threading.current_thread().name}: 队列为空，准备退出。")
            break # 队列为空，退出循环

        start_time = time.time()
        print(f"[Thread-{threading.current_thread().name}] 处理会话: {session_id}")

        # 获取会话元数据（客服名和客户名）
        session_meta = session_metadata.get(session_id, {})
        staff_name = session_meta.get("staff_name", "未知")
        customer_name = session_meta.get("user_name", "未知")
        member_id = session_meta.get("member_id", 0)  # 获取 member_id

        try:
            # 发送请求到 Prompt Service
            result = prompt_service.send_prompt(prompt_payload)
            end_time = time.time()
            duration = end_time - start_time
            print(f"[INFO] Session {session_id}: Prompt Service 请求耗时: {duration:.2f} 秒")

            with lock: # 使用锁保护共享计数器
                if result:
                    shared_counters['sessions_sent'] += 1
                    print(f"[INFO] Session {session_id}: Prompt Service 请求成功。")
                    # 处理返回的响应
                    if response_processor.process_response(session_id, result, staff_name, customer_name, member_id):
                        shared_counters['sessions_analysis_processed'] += 1
                    else:
                        shared_counters['sessions_analysis_failed'] += 1
                        print(f"[WARN] Session {session_id}: 处理 Prompt Service 响应失败。")
                else:
                    # send_prompt 内部应该已经打印了错误信息
                    shared_counters['sessions_send_failed'] += 1 # 新增发送失败计数器
                    print(f"[ERROR] Session {session_id}: 发送数据到 Prompt Service 失败。")

        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"[ERROR] Session {session_id}: 处理时发生意外错误 (耗时 {duration:.2f} 秒): {e}")
            with lock:
                shared_counters['sessions_processing_errors'] += 1 # 新增处理错误计数器

        finally:
            task_queue.task_done() # 标记任务完成

    # print(f"[DEBUG] 线程 {threading.current_thread().name}: 退出。")


def process_extracted_data(extract_dir, run_identifier):
    """处理解压后的七鱼会话数据，使用多线程发送到 Prompt Service。"""
    print(f"开始处理目录 {extract_dir} 中的数据 (运行标识: {run_identifier})...")
    
    # 初始化会话状态缓存
    session_cache = SessionStateCache()
    
    prompt_service = PromptServiceAPI(config.PROMPT_SERVICE_API_URL)
    # 初始化飞书机器人
    feishu_notifier = FeishuBotNotifier()
    # 初始化响应处理器，传入基础输出目录、运行标识符和飞书机器人实例
    response_processor = ResponseProcessor(
        base_output_dir=config.ANALYSIS_OUTPUT_DIR,
        run_identifier=run_identifier,
        feishu_notifier=feishu_notifier
    )
    
    # 初始化 StarRocks 存储
    starrocks_store = StarRocksStore(
        host=config.STARROCKS_HOST,
        port=config.STARROCKS_PORT,
        user=config.STARROCKS_USER,
        password=config.STARROCKS_PASSWORD,
        database=config.STARROCKS_DATABASE
    )
    
    # 连接数据库
    if starrocks_store.connect():
        print("[INFO] StarRocks 数据库连接成功")
    else:
        print("[WARN] StarRocks 数据库连接失败，数据将不会存储到数据库")

    # 初始化线程安全的计数器和锁
    shared_counters = defaultdict(int)
    lock = threading.Lock()

    # 创建任务队列
    task_queue = queue.Queue()
    total_sessions_to_process = 0

    # 创建 StarRocks 写入队列和停止标志
    sr_queue = queue.Queue()
    sr_stop_flag = threading.Event()

    # 启动 StarRocks 写入线程
    sr_thread = None
    if starrocks_store and starrocks_store.conn:
        sr_thread = threading.Thread(
            target=starrocks_writer_thread,
            args=(sr_queue, sr_stop_flag, starrocks_store),
            name="StarRocks-Writer"
        )
        sr_thread.daemon = True
        sr_thread.start()

    # 1. 首先处理 session.txt 文件,建立会话映射表
    session_mapping = {}  # {session_id: {"member_id": xxx, "staff_name": xxx, "user_name": xxx}}
    session_files = glob.glob(os.path.join(extract_dir, '**', 'session.txt'), recursive=True)
    
    if session_files:
        for session_file in session_files:
            print(f"处理会话文件: {session_file}")
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            # 每一行都先确认是 json 格式
                            if not line.strip().startswith('{'):
                                continue
                            session_data = json.loads(line.strip())
                            session_id = session_data.get("id")
                            if not session_id:
                                continue
                            
                            # 检查是否为机器人会话（staffId < 0）
                            staff_id = session_data.get("staffId")
                            is_robot_session = staff_id is not None and staff_id < 0
                            
                            # 机器人会话只写入数据库，不进行后续处理
                            if is_robot_session:
                                if starrocks_store and starrocks_store.conn:
                                    sr_queue.put(("session", session_data))
                                    print(f"[DEBUG] 跳过机器人会话 {session_id} (staffId={staff_id})，仅写入数据库")
                                continue
                                
                            # 提取 userCrmInfo 中的 member_id
                            member_id = 0
                            user_crm_info = session_data.get("userCrmInfo")
                            if user_crm_info:
                                try:
                                    crm_data = json.loads(user_crm_info)
                                    for item in crm_data:
                                        if item.get("key") == "member_id":
                                            try:
                                                member_id = int(item.get("value", "0"))
                                            except (ValueError, TypeError):
                                                member_id = 0
                                            break
                                except json.JSONDecodeError:
                                    print(f"[WARN] 解析 userCrmInfo 失败: {user_crm_info}")
                            
                            # 存储会话信息到映射表
                            session_mapping[session_id] = {
                                "member_id": member_id,
                                "staff_name": session_data.get("staffName"),
                                "user_name": session_data.get("userName"),
                                "staff_id": session_data.get("staffId"),
                                "user_id": session_data.get("userId")
                            }
                            
                            # 异步写入 StarRocks
                            if starrocks_store and starrocks_store.conn:
                                sr_queue.put(("session", session_data))
                                
                        except json.JSONDecodeError as e:
                            print(f"[ERROR] 解析会话数据失败: {e}")
                        except Exception as e:
                            print(f"[ERROR] 处理会话数据时发生错误: {e}")
            except Exception as e:
                print(f"[ERROR] 读取会话文件 {session_file} 失败: {e}")

    print(f"已处理 {len(session_mapping)} 个会话记录")

    # 2. 获取上次运行中还在进行的会话
    pending_sessions = session_cache.get_pending_sessions()
    completed_sessions = []
    
    # 检查哪些进行中的会话已结束
    for session_id in pending_sessions:
        if session_id in session_mapping:
            # 确认不是机器人会话
            staff_id = session_mapping[session_id].get("staff_id")
            if staff_id is not None and staff_id < 0:
                print(f"[DEBUG] 跳过机器人会话 {session_id}，不作为已完成会话处理")
                # 从待处理会话中移除机器人会话
                session_cache.remove_pending_session(session_id)
                continue
            
            # 该会话已经结束，需要处理
            completed_sessions.append(session_id)
            
    print(f"[DEBUG-跨窗口] 发现 {len(completed_sessions)} 个之前进行中现已完成的会话")
    if completed_sessions:
        sample_ids = completed_sessions[:min(5, len(completed_sessions))]
        print(f"[DEBUG-跨窗口] 已完成会话ID样本: {sample_ids}")
    
    # 3. 先处理 message.txt 文件，确保所有消息都写入数据库
    message_files = glob.glob(os.path.join(extract_dir, '**', 'message.txt'), recursive=True)
    if not message_files:
        print(f"在 {extract_dir} 中未找到 message.txt 文件。")
        # 保存会话缓存状态
        session_cache.save_cache()
        return
        
    # 收集本次消息中出现的所有会话ID
    current_message_sessions = set()
    # 保存当前会话的所有消息数据，用于后续合并
    current_session_messages = defaultdict(list)
    
    for msg_file_path in message_files:
        print(f"准备处理文件: {msg_file_path}")
        lines_processed = 0
        lines_failed_parse = 0
        sessions_in_file = 0

        session_data = defaultdict(list)

        try:
            # 读取所有消息并按 sessionId 分组
            with open(msg_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    lines_processed += 1
                    try:
                        cleaned_line = line.strip()
                        if lines_processed < 5:
                            print(f"[DEBUG] Line {line_num}: {cleaned_line[:200]}...")
                        
                        # 检查是否 json
                        if not cleaned_line.strip().startswith('{'):
                            continue
                        
                        # 解析 JSON
                        message_data = json.loads(cleaned_line)

                        # 提取session_id并添加到当前消息会话集合
                        session_id = message_data.get("sessionId")
                        if not session_id:
                            continue
                            
                        # 检查是否为机器人会话
                        staff_id = message_data.get("staffId")
                        if staff_id is not None and staff_id <= 0:
                            # 只写入数据库，不进行其他处理
                            if starrocks_store and starrocks_store.conn:
                                sr_queue.put(("message", message_data))
                            continue

                        # 将原始消息数据保存到当前会话消息集合，用于后续合并
                        msg_copy = dict(message_data)
                        current_session_messages[session_id].append(msg_copy)
                        current_message_sessions.add(session_id)
                            
                        # 存储 message 信息到 messages 表
                        if starrocks_store and starrocks_store.conn:
                            sr_queue.put(("message", message_data))

                        # 提取必要字段
                        if not session_id or session_id not in session_mapping:
                            # 跳过不在会话映射表中的消息
                            continue

                        # 处理消息内容
                        msg_content = message_data.get("msg", "")
                        processed_msg = process_msg_content(msg_content, message_data.get("mType"), message_data)
                            
                        # 将消息添加到对应的会话,并加入 member_id
                        session_info = session_mapping[session_id]
                        message_entry = {
                            "msg_id": message_data.get("id"),
                            "msg": processed_msg,
                            "from": message_data.get("from"),
                            "time": message_data.get("time"),
                            "time_iso": format_timestamp(message_data.get("time")),
                            "staff_id": message_data.get("staffId"),
                            "staff_name": session_info["staff_name"],
                            "user_id": session_info["user_id"],
                            "user_name": session_info["user_name"],
                            "member_id": session_info["member_id"],  # 从会话映射表中获取
                            "mType": message_data.get("mType"),
                            "autoReply": message_data.get("autoReply"),
                            "status": message_data.get("status")
                        }
                        
                        session_data[session_id].append(message_entry)
                            
                    except json.JSONDecodeError as json_err:
                        lines_failed_parse += 1
                        print(f"[WARN] 无法解析文件 {msg_file_path} 第 {line_num} 行 JSON: '{line.strip()[:200]}...' Error: {json_err}")
                    except Exception as inner_e:
                        print(f"[ERROR] 处理文件 {msg_file_path} 第 {line_num} 行时发生未知错误: {inner_e}")
                        
            # 将待处理的会话任务放入队列
            for session_id, messages in session_data.items():
                sessions_in_file += 1
                try:
                    # 检查该会话是否在已完成列表中，如果是则跳过，后面会作为完整会话处理
                    if session_id in completed_sessions:
                        print(f"[DEBUG-跨窗口] 跳过处理会话 {session_id} 的当前消息，稍后将作为完整会话处理")
                        continue
                        
                    sorted_messages = sorted(messages, key=lambda x: x.get("time", 0))
                    session_info = session_mapping[session_id]

                    conversation_data = {
                        "session_id": session_id,
                        "staff": {
                            "id": session_info["staff_id"],
                            "name": session_info["staff_name"]
                        },
                        "customer": {
                            "id": session_info["user_id"],
                            "name": session_info["user_name"],
                            "member_id": session_info["member_id"]
                        },
                        "messages": []
                    }
                    
                    for msg in sorted_messages:
                        speaker = "客服" if msg.get("from") == 0 else "客户"
                        speaker_name = session_info["staff_name"] if msg.get("from") == 0 else session_info["user_name"]
                        message_entry = {
                            "speaker": speaker,
                            "speaker_name": speaker_name,
                            "content": msg.get("msg", ""),
                            "time": msg.get("time_iso", "")
                        }
                        conversation_data["messages"].append(message_entry)

                    formatted_data = format_conversation(conversation_data)
                    if not formatted_data:
                        print(f"[WARN] 会话 {session_id} 格式化后的数据为空，跳过放入队列")
                        continue
                        
                    # 异步写入对话数据到 StarRocks
                    if starrocks_store and starrocks_store.conn:
                        conversation_storage_data = {
                            'session_id': session_id,
                            'conversation_text': formatted_data,
                            'staff_name': session_info["staff_name"],
                            'user_name': session_info["user_name"],
                            'member_id': session_info["member_id"]
                        }
                        # 不在这里写入，而是在处理完所有消息后统一写入
                        # sr_queue.put(("conversation", conversation_storage_data))

                    prompt_payload = {
                        "id": config.PROMPT_SERVICE_ID,
                        "Q": formatted_data,
                        "ref": "",
                        "language": "zh-CN",
                        "grpc_timeout": config.GRPC_TIME
                    }

                    task_queue.put((session_id, prompt_payload))
                    total_sessions_to_process += 1

                except Exception as e:
                    print(f"[ERROR] 准备会话 {session_id} 数据时发生错误: {e}")

            print(f"文件 {msg_file_path} 读取完毕: 总行数={lines_processed}, 解析失败={lines_failed_parse}, 文件中会话数={sessions_in_file}")

        except FileNotFoundError:
            print(f"[ERROR] 文件未找到: {msg_file_path}")
        except Exception as e:
            print(f"[ERROR] 读取或预处理文件 {msg_file_path} 时发生错误: {e}")

    # 找出消息中存在但会话映射中不存在的ID（这些是进行中的会话）
    in_progress_sessions = current_message_sessions - set(session_mapping.keys())
    
    # 将这些进行中的会话添加到缓存
    for session_id in in_progress_sessions:
        session_cache.add_pending_session(session_id)
        
    print(f"[DEBUG-跨窗口] 发现 {len(in_progress_sessions)} 个正在进行中的会话，已添加到缓存")
    if in_progress_sessions:
        sample_ids = list(in_progress_sessions)[:min(5, len(in_progress_sessions))]
        print(f"[DEBUG-跨窗口] 进行中会话ID样本: {sample_ids}")
    
    # 等待数据库写入队列处理完成
    print("[DEBUG-跨窗口] 等待所有消息写入数据库...")
    sr_queue.join()
    print("[DEBUG-跨窗口] 所有消息已写入数据库，开始处理已完成会话")
    
    # 4. 处理已完成的会话，从数据库获取完整记录 (确保在所有消息都写入后执行)
    if completed_sessions and starrocks_store and starrocks_store.conn:
        completed_queue = queue.Queue()
        processing_thread = None
        
        # 创建一个单独的线程处理已完成的会话
        def process_completed_sessions():
            while True:
                # 定义一个session_id变量，在外层处理异常
                session_id = None
                try:
                    session_id = completed_queue.get(timeout=3)
                    print(f"[DEBUG-跨窗口] 开始处理已完成的会话: {session_id}")
                    
                    # 从数据库获取历史消息记录
                    db_messages = starrocks_store.get_complete_session_messages(session_id)
                    if not db_messages or len(db_messages) == 0:
                        print(f"[WARN] 会话 {session_id} 没有找到历史消息记录")
                        continue
                    
                    print(f"[DEBUG-跨窗口] 会话 {session_id} 从数据库获取到 {len(db_messages)} 条历史消息记录")
                    
                    # 获取当前运行时处理的会话消息
                    current_messages = current_session_messages.get(session_id, [])
                    print(f"[DEBUG-跨窗口] 会话 {session_id} 当前运行中处理了 {len(current_messages)} 条消息")
                    
                    # 创建消息合并集合，使用消息ID作为去重标准
                    all_messages = {}
                    
                    # 添加数据库消息到合并集合
                    for msg in db_messages:
                        msg_id = msg.get("id")
                        if msg_id:
                            all_messages[msg_id] = msg
                    
                    # 添加当前消息到合并集合（可能会覆盖同ID的数据库消息，保留最新）
                    for msg in current_messages:
                        msg_id = msg.get("id")
                        if msg_id:
                            all_messages[msg_id] = msg
                    
                    # 将合并后的消息转换为列表并按时间排序
                    combined_messages = list(all_messages.values())
                    combined_messages.sort(key=lambda x: x.get("time", 0))

                    print(f"[DEBUG-跨窗口] 会话 {session_id} 合并后共有 {len(combined_messages)} 条消息")
                    
                    # 检查合并后的消息是否为空
                    if not combined_messages:
                        print(f"[WARN] 会话 {session_id} 合并后的消息为空，跳过处理")
                        continue
                    
                    # 构造完整对话数据
                    session_info = session_mapping[session_id]
                    conversation_data = {
                        "session_id": session_id,
                        "staff": {
                            "id": session_info["staff_id"],
                            "name": session_info["staff_name"]
                        },
                        "customer": {
                            "id": session_info["user_id"],
                            "name": session_info["user_name"],
                            "member_id": session_info["member_id"]
                        },
                        "messages": []
                    }
                    
                    # 构造消息列表
                    message_times = []
                    for msg in combined_messages:
                        # 过滤机器人消息
                        staff_id = msg.get("staffId")
                        if staff_id is not None and staff_id <= 0:
                            continue
                            
                        speaker = "客服" if msg.get("from") == 0 else "客户"
                        speaker_name = session_info["staff_name"] if msg.get("from") == 0 else session_info["user_name"]
                        time_stamp = msg.get("time", 0)
                        message_times.append(time_stamp)
                        
                        # 处理消息内容
                        msg_content = msg.get("msg", "")
                        processed_msg = process_msg_content(msg_content, msg.get("mType"), msg)
                        
                        message_entry = {
                            "speaker": speaker,
                            "speaker_name": speaker_name,
                            "content": processed_msg,  # 使用处理后的消息内容
                            "time": format_timestamp(time_stamp)
                        }
                        conversation_data["messages"].append(message_entry)
                    
                    # 记录会话时间范围
                    if message_times:
                        start_time = min(message_times)
                        end_time = max(message_times)
                        time_span = (end_time - start_time) / (1000 * 60)  # 转换为分钟
                        print(f"[DEBUG-跨窗口] 会话 {session_id} 合并后时间跨度: {time_span:.2f} 分钟")
                        print(f"[DEBUG-跨窗口] 会话起止时间: {format_timestamp(start_time)} -> {format_timestamp(end_time)}")
                    
                    # 格式化对话内容
                    formatted_data = format_conversation(conversation_data)
                    if not formatted_data:
                        print(f"[WARN] 会话 {session_id} 格式化后的数据为空，跳过")
                        continue
                    
                    # 记录对话轮次
                    rounds = len(conversation_data["messages"])
                    print(f"[DEBUG-跨窗口] 会话 {session_id} 合并后共有 {rounds} 轮对话")
                    
                    # 写入完整的会话数据到 conversations 表
                    if starrocks_store and starrocks_store.conn:
                        conversation_storage_data = {
                            'session_id': session_id,
                            'conversation_text': formatted_data,
                            'staff_name': session_info["staff_name"],
                            'user_name': session_info["user_name"],
                            'member_id': session_info["member_id"]
                        }
                        sr_queue.put(("conversation", conversation_storage_data))
                        
                    # 创建 prompt 任务
                    prompt_payload = {
                        "id": config.PROMPT_SERVICE_ID,
                        "Q": formatted_data,
                        "ref": "",
                        "language": "zh-CN",
                        "grpc_timeout": config.GRPC_TIME
                    }
                    
                    # 将任务加入队列
                    task_queue.put((session_id, prompt_payload))
                    with lock:
                        nonlocal total_sessions_to_process
                        total_sessions_to_process += 1
                    
                    # 从待处理会话中移除
                    session_cache.remove_pending_session(session_id)
                    
                    # 从当前会话处理列表中移除，避免重复处理
                    if session_id in session_data:
                        del session_data[session_id]
                    
                    print(f"[INFO] 已处理之前进行中现已完成的会话: {session_id}")
                    
                except queue.Empty:
                    # 队列为空时直接退出循环
                    print("[DEBUG-跨窗口] 已完成会话处理队列为空，处理线程退出")
                    break
                except Exception as e:
                    print(f"[ERROR] 处理已完成会话 {session_id} 时出错: {e}")
                finally:
                    # 只有在成功获取了任务时才标记完成
                    if session_id is not None:
                        completed_queue.task_done()
        
        # 将已完成的会话ID添加到队列
        for session_id in completed_sessions:
            completed_queue.put(session_id)
            
        # 启动处理线程
        processing_thread = threading.Thread(
            target=process_completed_sessions,
            name="CompletedSessionProcessor"
        )
        processing_thread.daemon = True
        processing_thread.start()
        
        # 等待处理线程完成
        completed_queue.join()
    
    # 保存会话缓存状态
    session_cache.save_cache()

    # 创建并启动工作线程
    threads = []
    num_threads = min(config.MAX_CONCURRENT_THREADS, total_sessions_to_process)
    if num_threads <= 0:
        print("没有需要处理的会话任务。")
        return

    print(f"\n启动 {num_threads} 个工作线程处理 {total_sessions_to_process} 个会话...")
    for i in range(num_threads):
        thread = threading.Thread(
            target=worker_thread,
            args=(task_queue, prompt_service, response_processor, shared_counters, lock, session_mapping),
            name=f"Worker-{i+1}"
        )
        thread.daemon = True
        thread.start()
        threads.append(thread)

    # 等待所有任务完成
    task_queue.join()
    print("\n所有任务已处理完毕。")

    # 停止 StarRocks 写入线程
    if sr_thread:
        sr_stop_flag.set()
        sr_queue.put(None)  # 发送结束信号
        sr_thread.join(timeout=30)  # 等待写入线程结束，最多等待30秒

    # 打印最终统计结果
    print("\n--- 处理统计 ---")
    print(f"总共处理会话数: {total_sessions_to_process}")
    print(f"发送成功数: {shared_counters['sessions_sent']}")
    print(f"发送失败数: {shared_counters['sessions_send_failed']}")
    print(f"分析处理成功数: {shared_counters['sessions_analysis_processed']}")
    print(f"分析处理失败数: {shared_counters['sessions_analysis_failed']}")
    print(f"处理中发生错误数: {shared_counters['sessions_processing_errors']}")
    print("------------------")
    
    # 断开数据库连接
    if starrocks_store:
        starrocks_store.disconnect()
        print("[INFO] 已断开与 StarRocks 数据库的连接")

def starrocks_writer_thread(queue, stop_flag, starrocks_store):
    """StarRocks 数据写入线程"""
    while not stop_flag.is_set():
        try:
            item = queue.get(timeout=1)
            if item is None:  # 结束信号
                break
                
            data_type, data = item
            try:
                if data_type == "session":
                    starrocks_store.store_session(data)
                elif data_type == "conversation":
                    starrocks_store.store_conversation(data)
                elif data_type == "message":
                    starrocks_store.store_message(data)
            except Exception as e:
                print(f"[ERROR] StarRocks 写入失败 ({data_type}): {e}")
            finally:
                queue.task_done()
                
        except Empty:  # 使用导入的 Empty 异常
            continue
        except Exception as e:
            print(f"[ERROR] StarRocks 写入线程发生错误: {e}")
            if not stop_flag.is_set():  # 如果不是正常停止，等待一会再继续
                time.sleep(1)
            
    print("[INFO] StarRocks 写入线程已退出")

def process_msg_content(msg_content, msg_type, message_data=None):
    """处理消息内容，解析嵌套的 JSON 和富文本，并添加相关上下文信息"""
    if not msg_content:
        return ""
    
    # 提取额外的消息属性（如果提供）
    prefixes = []
    suffixes = []
    
    if message_data:
        # 检查是否为机器人消息
        staff_id = message_data.get("staffId")
        if staff_id is not None and staff_id <= 0:
            prefixes.append("[机器人]")
        
        # 检查是否为自动回复
        auto_reply = message_data.get("autoReply")
        if auto_reply == 1:
            prefixes.append("[自动回复]")
        
        # 检查消息状态
        status = message_data.get("status")
        if status == 2:
            prefixes.append("[已撤回]")
        
        # 处理机器人答案评价（只对机器人答案添加评价信息）
        if msg_type == 110:
            evaluation = message_data.get("evaluation")
            if evaluation:
                eval_desc = EVALUATION_DESC.get(evaluation, f"未知评价({evaluation})")
                suffixes.append(f"[评价: {eval_desc}]")
            
            # 添加匹配知识库信息
            match_type = message_data.get("matchType")
            match_knowledge_id = message_data.get("matchKnowledgeId")
            if match_type and match_type > 0:
                match_desc = MATCH_TYPE_DESC.get(match_type, f"未知匹配({match_type})")
                if match_knowledge_id and match_knowledge_id > 0:
                    suffixes.append(f"[{match_desc}, 知识库ID: {match_knowledge_id}]")
                else:
                    suffixes.append(f"[{match_desc}]")
    
    # 根据消息类型进行特殊处理
    if msg_type in [0, 6, 112, 113, 116, 117, 118, 119, 121, 122, 124]:
        # 系统类型消息，添加类型标记
        prefixes.append(f"[{get_message_type_desc(msg_type)}]")
        content = f"{' '.join(prefixes)} {msg_content}"
        if suffixes:
            content = f"{content} {' '.join(suffixes)}"
        return content
    
    elif msg_type in [2, 3, 4, 5]:
        # 媒体类型消息，替换为类型描述
        return f"{' '.join(prefixes)} [{get_message_type_desc(msg_type)}] {' '.join(suffixes)}".strip()
    
    # 对于嵌套 JSON 的处理
    if isinstance(msg_content, str) and (msg_content.startswith("{") or msg_content.startswith("[")):
        try:
            msg_json = json.loads(msg_content)
            
            # 处理富文本消息 (mType = 115)
            if msg_type == 115 and isinstance(msg_json, dict) and "content" in msg_json:
                content = msg_json.get("content", "")
                clean_text = html.unescape(re.sub(r'<[^>]+>', ' ', content)).strip()
                result = f"{' '.join(prefixes)} {clean_text}".strip()
                if suffixes:
                    result = f"{result} {' '.join(suffixes)}"
                return result
            
            # 处理机器人答案 (mType = 110)
            elif msg_type == 110 and isinstance(msg_json, dict):
                prefix = f"{' '.join(prefixes)} [机器人回答]".strip()
                if "content" in msg_json:
                    result = f"{prefix} {msg_json.get('content', '')}"
                else:
                    result = f"{prefix} {json.dumps(msg_json, ensure_ascii=False)}"
                
                if suffixes:
                    result = f"{result} {' '.join(suffixes)}"
                return result
            
            # 处理emoji消息 (mType = 123)
            elif msg_type == 123 and isinstance(msg_json, dict):
                prefix = f"{' '.join(prefixes)} [表情]".strip()
                if "content" in msg_json:
                    result = f"{prefix} {msg_json.get('content', '')}"
                else:
                    result = prefix
                
                if suffixes:
                    result = f"{result} {' '.join(suffixes)}"
                return result
            
            # 处理自定义消息 (mType = 100)
            elif msg_type == 100:
                prefix = f"{' '.join(prefixes)} [自定义消息]".strip()
                result = f"{prefix} {json.dumps(msg_json, ensure_ascii=False, indent=2)}"
                if suffixes:
                    result = f"{result} {' '.join(suffixes)}"
                return result
            
            # 其他 JSON 结构
            result = f"{' '.join(prefixes)}".strip()
            if result:
                result += " "
            result += json.dumps(msg_json, ensure_ascii=False, indent=2)
            if suffixes:
                result = f"{result} {' '.join(suffixes)}"
            return result
            
        except json.JSONDecodeError:
            # 如果解析失败，返回原始内容并附加类型信息
            if msg_type != 1:  # 非文本消息添加类型标记
                result = f"{' '.join(prefixes)} [{get_message_type_desc(msg_type)}] {msg_content}".strip()
            else:
                result = f"{' '.join(prefixes)} {msg_content}".strip()
            
            if suffixes:
                result = f"{result} {' '.join(suffixes)}"
            return result
    
    # 对于普通文本消息，添加前缀和后缀
    result = f"{' '.join(prefixes)} {msg_content}".strip()
    if suffixes:
        result = f"{result} {' '.join(suffixes)}"
    return result

def format_conversation(conversation_data):
    """将对话数据格式化为结构化的字符串，方便大模型分析"""
    if not conversation_data or not conversation_data.get("messages"):
        return ""
        
    session_id = conversation_data.get("session_id", "未知")
    staff_info = conversation_data.get("staff", {})
    customer_info = conversation_data.get("customer", {})
    messages = conversation_data.get("messages", [])
    
    # 构建格式化字符串
    result = []
    result.append(f"会话ID: {session_id}")
    result.append(f"客服信息: ID={staff_info.get('id', '未知')}, 姓名={staff_info.get('name', '未知')}")
    result.append(f"客户信息: ID={customer_info.get('id', '未知')}, 姓名={customer_info.get('name', '未知')}")
    result.append("\n对话内容:")
    
    for i, msg in enumerate(messages, 1):
        speaker = msg.get("speaker", "未知")
        speaker_name = msg.get("speaker_name", "未知")
        content = msg.get("content", "")
        time = msg.get("time", "")
        
        # 如果内容是空的，跳过
        if not content:
            continue
            
        result.append(f"{i}. {speaker}({speaker_name}) [{time}]: {content}")
    
    return "\n".join(result)

class SessionStateCache:
    def __init__(self, max_size=10000):
        """初始化会话状态缓存
        
        Args:
            max_size: 最大缓存会话数，防止内存溢出
        """
        self.pending_sessions = set()  # 存储进行中的会话ID
        self.max_size = max_size
        self.cache_file = os.path.join(config.ANALYSIS_OUTPUT_DIR, "pending_sessions.json")
        self.load_cache()
        
    def load_cache(self):
        """从文件加载缓存的会话ID"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.pending_sessions = set(data.get("pending_sessions", []))
                    print(f"[DEBUG-跨窗口] 已从缓存加载 {len(self.pending_sessions)} 个进行中的会话")
                    if len(self.pending_sessions) > 0:
                        sample_ids = list(self.pending_sessions)[:5]  # 取前5个样本
                        print(f"[DEBUG-跨窗口] 进行中会话ID样本: {sample_ids}")
            except Exception as e:
                print(f"[WARN] 加载会话缓存失败: {e}")
                
    def save_cache(self):
        """保存缓存的会话ID到文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump({"pending_sessions": list(self.pending_sessions)}, f)
            print(f"[DEBUG-跨窗口] 已保存 {len(self.pending_sessions)} 个进行中的会话到缓存")
            if len(self.pending_sessions) > 0:
                sample_ids = list(self.pending_sessions)[:5]  # 取前5个样本
                print(f"[DEBUG-跨窗口] 新增进行中会话ID样本: {sample_ids}")
        except Exception as e:
            print(f"[WARN] 保存会话缓存失败: {e}")
            
    def add_pending_session(self, session_id):
        """添加一个进行中的会话ID"""
        if len(self.pending_sessions) >= self.max_size:
            # 如果超过最大大小，随机移除一个旧的会话ID
            if self.pending_sessions:
                removed = self.pending_sessions.pop()
                print(f"[DEBUG-跨窗口] 缓存已满，移除旧会话ID: {removed}")
        self.pending_sessions.add(session_id)
        
    def remove_pending_session(self, session_id):
        """移除一个已完成的会话ID"""
        if session_id in self.pending_sessions:
            self.pending_sessions.remove(session_id)
            print(f"[DEBUG-跨窗口] 已从缓存中移除已完成会话: {session_id}")
            
    def get_pending_sessions(self):
        """获取所有进行中的会话ID"""
        return self.pending_sessions.copy()
