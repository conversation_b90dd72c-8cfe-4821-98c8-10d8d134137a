import subprocess
from datetime import datetime, timedelta

# 定义起始日期和结束日期
start_date = datetime.strptime("2025-12-01", "%Y-%m-%d")
end_date = datetime.strptime("2025-12-31", "%Y-%m-%d")

# 当前日期指针
current_date = start_date

# 循环生成并执行命令
while current_date < end_date:
    # 计算窗口结束日期
    window_end_date = current_date + timedelta(days=2)
    if window_end_date > end_date:
        window_end_date = end_date  # 确保不超过结束日期

    # 格式化日期为字符串
    start_date_str = current_date.strftime("%Y-%m-%d")
    end_date_str = window_end_date.strftime("%Y-%m-%d")

    # 构造命令
    command = f"python qa_processor.py --start_date \"{start_date_str}\" --end_date \"{end_date_str}\" --threads 24"
    print(f"Executing: {command}")

    # 执行命令
    subprocess.run(command, shell=True)

    # 更新当前日期指针
    current_date = window_end_date

