#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import subprocess
import glob
import sys
from pathlib import Path

def get_all_qa_directories():
    """获取所有qa_results下的日期目录"""
    qa_results_dir = "qa_results"
    if not os.path.exists(qa_results_dir):
        print(f"错误: {qa_results_dir} 目录不存在")
        return []
    
    # 查找所有符合日期格式的目录
    pattern = os.path.join(qa_results_dir, "*_to_*/")
    directories = glob.glob(pattern)
    
    # 过滤出有 qa_pairs 子目录的目录
    valid_directories = []
    for directory in directories:
        qa_pairs_dir = os.path.join(directory, "qa_pairs")
        if os.path.exists(qa_pairs_dir) and os.path.isdir(qa_pairs_dir):
            valid_directories.append(qa_pairs_dir)
    
    # 按目录名排序
    valid_directories.sort()
    return valid_directories

def main():
    """主函数"""
    print("开始处理所有日期文件夹的 QA 数据...")
    print("=" * 80)
    
    # 获取所有有效的qa_pairs目录
    qa_directories = get_all_qa_directories()
    
    if not qa_directories:
        print("没有找到有效的 qa_pairs 目录")
        return
    
    print(f"找到 {len(qa_directories)} 个有效的 qa_pairs 目录:")
    for i, directory in enumerate(qa_directories, 1):
        print(f"  {i}. {directory}")
    
    print("\n" + "=" * 80)
    
    # 处理每个目录
    total_processed = 0
    total_failed = 0
    
    for i, qa_dir in enumerate(qa_directories, 1):
        print(f"\n[{i}/{len(qa_directories)}] 正在处理: {qa_dir}")
        print("-" * 60)
        
        # 构造命令
        command = [
            "python", "qa_normalize.py",
            "--qa_dir", qa_dir,
            "--concurrent",
            "--max_concurrent", "20",
            "--process_batch_size", "200",
            "--db_batch_size", "1000",
            "--threshold", "0.84",
            "--fallback_threshold", "0.8"
        ]
        
        print(f"执行命令: {' '.join(command)}")
        
        try:
            # 执行命令
            result = subprocess.run(command, check=True, capture_output=False)
            print(f"✅ 处理完成: {qa_dir}")
            total_processed += 1
        except subprocess.CalledProcessError as e:
            print(f"❌ 处理失败: {qa_dir}")
            print(f"   错误代码: {e.returncode}")
            total_failed += 1
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断了处理过程")
            break
        except Exception as e:
            print(f"❌ 处理异常: {qa_dir}")
            print(f"   错误信息: {str(e)}")
            total_failed += 1
    
    # 打印总结
    print("\n" + "=" * 80)
    print("处理完成 - 总结:")
    print(f"  总目录数: {len(qa_directories)}")
    print(f"  处理成功: {total_processed}")
    print(f"  处理失败: {total_failed}")
    print("=" * 80)

if __name__ == "__main__":
    main() 