import json
import pymysql
import config
import queue
import threading
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from utils import parse_time

class StarRocksStore:
    """用于将数据异步存储到 StarRocks 数据库的类"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str, batch_size: int = 1000):
        """
        初始化 StarRocks 数据库连接
        
        Args:
            host (str): 数据库主机地址
            port (int): 数据库端口
            user (str): 数据库用户名
            password (str): 数据库密码
            database (str): 数据库名称
            batch_size (int): 批量插入的大小，默认为1000
        """
        self.connection_params = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'init_command': "SET SESSION time_zone='+08:00'"
        }
        self.conn = None
        self.cursor = None
        self.batch_size = batch_size
        
        # 创建数据队列
        self.data_queue = queue.Queue()
        
        # 批量数据缓存
        self.batch_data = {
            'user_complaints': [],
            'sessions': [],
            'messages': [],
            'conversations': [],
            'qa_pairs': [],  # 添加QA对的批量缓存
            'structured_conversations': []  # 添加结构化会话的批量缓存
        }
        
        # 批量数据锁
        self.batch_lock = threading.Lock()
        
        # 添加统计信息
        self.stats = {
            'total_queued': 0,
            'total_processed': 0,
            'batch_flushes': 0,
            'qa_pairs_queued': 0,
            'qa_pairs_flushed': 0,
            'structured_conversations_queued': 0,
            'structured_conversations_flushed': 0
        }
        
        print(f"[INFO] StarRocks存储初始化，批量大小: {self.batch_size}")
        
        # 创建工作线程
        self.worker_thread = threading.Thread(target=self._process_queue, daemon=True)
        self.is_running = True
        self.worker_thread.start()
        
    def connect(self) -> bool:
        """连接到 StarRocks 数据库"""
        try:
            if self.conn is None or not self.conn.open:
                self.conn = pymysql.connect(**self.connection_params)
                self.cursor = self.conn.cursor()
                print("[INFO] 成功连接到 StarRocks 数据库")
            return True
        except Exception as e:
            print(f"[ERROR] 连接 StarRocks 数据库失败: {e}")
            return False
            
    def disconnect(self):
        """断开与 StarRocks 数据库的连接"""
        self.is_running = False  # 停止工作线程
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)  # 等待工作线程完成
            
        # 确保所有剩余数据都被处理
        self._flush_all_batches()
            
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            print("[INFO] 已断开与 StarRocks 数据库的连接")
            
    def _process_queue(self):
        """处理队列中的数据的工作线程"""
        while self.is_running:
            try:
                # 获取队列中的任务，设置超时以便定期检查 is_running 状态
                try:
                    task = self.data_queue.get(timeout=1)
                except queue.Empty:
                    # 检查是否有需要刷新的批量数据
                    self._check_and_flush_batches()
                    continue
                
                # 确保数据库连接
                if not self.connect():
                    print("[ERROR] 数据库连接失败，重新入队任务")
                    self.data_queue.put(task)
                    time.sleep(5)  # 等待一段时间后重试
                    continue
                
                try:
                    # 执行存储操作
                    table_name = task['table']
                    data = task['data']
                    method = task['method']
                    
                    # 将数据添加到批量缓存
                    with self.batch_lock:
                        if method == 'store_complaint':
                            self.batch_data['user_complaints'].append((data['session_id'], data['analysis_data']))
                        elif method == 'store_session':
                            self.batch_data['sessions'].append(data)
                        elif method == 'store_message':
                            self.batch_data['messages'].append(data)
                        elif method == 'store_conversation':
                            self.batch_data['conversations'].append(data)
                        elif method == 'store_qa_pair':
                            self.batch_data['qa_pairs'].append(data)
                            self.stats['qa_pairs_queued'] += 1
                            print(f"[DEBUG] QA对已加入批量缓存，当前缓存数量: {len(self.batch_data['qa_pairs'])}, 总排队数: {self.stats['qa_pairs_queued']}")
                        elif method == 'store_structured_conversation':
                            self.batch_data['structured_conversations'].append(data)
                            self.stats['structured_conversations_queued'] += 1
                            print(f"[DEBUG] 结构化会话已加入批量缓存，当前缓存数量: {len(self.batch_data['structured_conversations'])}, 总排队数: {self.stats['structured_conversations_queued']}")
                        
                        self.stats['total_queued'] += 1
                    
                    # 检查是否需要刷新批量数据
                    self._check_and_flush_batches()
                    
                    self.data_queue.task_done()
                    self.stats['total_processed'] += 1
                except Exception as e:
                    print(f"[ERROR] 处理任务失败: {e}")
                    # 重新入队
                    self.data_queue.put(task)
                    time.sleep(1)  # 避免立即重试
                    
            except Exception as e:
                print(f"[ERROR] 工作线程发生错误: {e}")
                time.sleep(1)  # 避免过于频繁的错误日志
                
    def _check_and_flush_batches(self):
        """检查并刷新批量数据"""
        with self.batch_lock:
            for table_name, data_list in self.batch_data.items():
                if len(data_list) >= self.batch_size:
                    print(f"[DEBUG] 表 {table_name} 达到批量大小 {len(data_list)}/{self.batch_size}，开始刷新")
                    self._flush_batch(table_name, data_list)
                    self.batch_data[table_name] = []
    
    def _flush_all_batches(self):
        """刷新所有批量数据"""
        with self.batch_lock:
            print(f"[DEBUG] 开始刷新所有批量数据...")
            for table_name, data_list in self.batch_data.items():
                if data_list:
                    print(f"[DEBUG] 刷新表 {table_name}，数据量: {len(data_list)}")
                    self._flush_batch(table_name, data_list)
                    self.batch_data[table_name] = []
            
            # 打印统计信息
            print(f"[INFO] 批量处理统计: 总排队={self.stats['total_queued']}, 总处理={self.stats['total_processed']}, 批量刷新次数={self.stats['batch_flushes']}")
            print(f"[INFO] QA对统计: 排队={self.stats['qa_pairs_queued']}, 已刷新={self.stats['qa_pairs_flushed']}")
            print(f"[INFO] 结构化会话统计: 排队={self.stats['structured_conversations_queued']}, 已刷新={self.stats['structured_conversations_flushed']}")
    
    def _flush_batch(self, table_name: str, data_list: List[Any]):
        """刷新特定表的批量数据"""
        if not data_list:
            return
            
        try:
            if not self._ensure_connection():
                print(f"[ERROR] 无法连接到数据库，无法刷新 {table_name} 的批量数据")
                return
                
            print(f"[DEBUG] 开始批量插入 {len(data_list)} 条数据到 {table_name} 表")
            
            # 初始化success变量
            success = False
                
            if table_name == 'user_complaints':
                success = self._batch_store_complaint_data(data_list)
            elif table_name == 'sessions':
                success = self._batch_store_session_data(data_list)
            elif table_name == 'messages':
                success = self._batch_store_message_data(data_list)
            elif table_name == 'conversations':
                success = self._batch_store_conversation_data(data_list)
            elif table_name == 'qa_pairs':
                success = self._batch_store_qa_pair_data(data_list)
                if success:
                    self.stats['qa_pairs_flushed'] += len(data_list)
            elif table_name == 'structured_conversations':
                success = self._batch_store_structured_conversation_data(data_list)
                if success:
                    self.stats['structured_conversations_flushed'] += len(data_list)
            else:
                print(f"[ERROR] 未知的表名: {table_name}")
                return
                
            if success:
                print(f"[INFO] 成功批量插入 {len(data_list)} 条数据到 {table_name} 表")
                self.stats['batch_flushes'] += 1
            else:
                print(f"[ERROR] 批量插入 {table_name} 表失败")
                
        except Exception as e:
            print(f"[ERROR] 刷新 {table_name} 批量数据失败: {e}")
            import traceback
            traceback.print_exc()
                
    def _ensure_connection(self) -> bool:
        """确保数据库连接可用"""
        try:
            if self.conn is None or not self.conn.open:
                return self.connect()
            return True
        except Exception:
            return self.connect()
            
    def _execute_query(self, query: str, params: tuple) -> bool:
        """执行 SQL 查询"""
        try:
            if not self._ensure_connection():
                return False
            self.cursor.execute(query, params)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"[ERROR] 执行查询失败: {e}")
            print(f"查询: {query}")
            print(f"参数: {params}")
            return False
            
    def _execute_batch_query(self, query: str, params_list: List[tuple]) -> bool:
        """执行批量 SQL 查询"""
        try:
            if not self._ensure_connection():
                return False
            self.cursor.executemany(query, params_list)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"[ERROR] 执行批量查询失败: {e}")
            print(f"查询: {query}")
            print(f"参数数量: {len(params_list)}")
            return False
            
    def _store_complaint_data(self, session_id: str, data: Dict[str, Any]) -> bool:
        """
        存储投诉分析数据（单条）
        
        Args:
            session_id (str): 会话ID
            data (dict): 分析数据
        """
        try:
            # 提取需要的字段
            user_demand = data.get("用户诉求", {}).get("用户核心诉求", "")
            demand_evidence = json.dumps(data.get("用户诉求", {}).get("佐证", []), ensure_ascii=False)

            keyword_match = data.get("关键词匹配", {}).get("匹配结论", False)
            keywords = json.dumps(data.get("关键词匹配", {}).get("具体词汇及频次", {}), ensure_ascii=False)

            emotion_index = data.get("用户情绪", {}).get("情绪指数", "")
            emotion_type = data.get("用户情绪", {}).get("情绪类型", "")
            emotion_intensity = data.get("用户情绪", {}).get("强度判断", "")
            emotion_evidence = json.dumps(data.get("用户情绪", {}).get("佐证", []), ensure_ascii=False)

            dialogue_rounds = data.get("对话模式", {}).get("对话轮次", 0)
            start_time = data.get("对话模式", {}).get("会话开始时间", "")
            end_time = data.get("对话模式", {}).get("会话结束时间", "")
            # duration 需要转换为 int
            duration = int(data.get("对话模式", {}).get("对话时长", 0))
            service_efficiency = data.get("对话模式", {}).get("客服处理效率", "")
            efficiency_reason = data.get("对话模式", {}).get("理由", "")

            complaint_type = data.get("风险点识别", {}).get("投诉类型", "")
            risk_level = data.get("风险点识别", {}).get("投诉风险", "")
            risk_evidence = json.dumps(data.get("风险点识别", {}).get("佐证", []), ensure_ascii=False)

            resolution_result = data.get("解决结果", {}).get("客服处理结果", "")

            rule_match = data.get("规则判断", {}).get("规则命中", "")
            rule_level = data.get("规则判断", {}).get("规则命中等级", "")

            return self._execute_query(
                """
                INSERT INTO user_complaints (session_id, user_demand, demand_evidence, keyword_match, keywords,
                 emotion_index, emotion_type, emotion_intensity, emotion_evidence,
                 dialogue_rounds, start_time, end_time, duration, service_efficiency, efficiency_reason,
                 complaint_type, risk_level, risk_evidence, resolution_result,
                 rule_match, rule_level) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (session_id, user_demand, demand_evidence, keyword_match, keywords,
                 emotion_index, emotion_type, emotion_intensity, emotion_evidence,
                 dialogue_rounds, start_time, end_time, duration, service_efficiency, efficiency_reason,
                 complaint_type, risk_level, risk_evidence, resolution_result,
                 rule_match, rule_level)
            )
        except Exception as e:
            print(f"[ERROR] 存储投诉数据失败: {e}")
            return False
            
    def _batch_store_complaint_data(self, data_list: List[Tuple[str, Dict[str, Any]]]) -> bool:
        """
        批量存储投诉分析数据
        
        Args:
            data_list (List[Tuple[str, Dict[str, Any]]]): 会话ID和分析数据的列表
        """
        try:
            params_list = []
            for session_id, data in data_list:
                # 提取需要的字段
                user_demand = data.get("用户诉求", {}).get("用户核心诉求", "")
                demand_evidence = json.dumps(data.get("用户诉求", {}).get("佐证", []), ensure_ascii=False)

                keyword_match = data.get("关键词匹配", {}).get("匹配结论", False)
                keywords = json.dumps(data.get("关键词匹配", {}).get("具体词汇及频次", {}), ensure_ascii=False)

                emotion_index = data.get("用户情绪", {}).get("情绪指数", "")
                emotion_type = data.get("用户情绪", {}).get("情绪类型", "")
                emotion_intensity = data.get("用户情绪", {}).get("强度判断", "")
                emotion_evidence = json.dumps(data.get("用户情绪", {}).get("佐证", []), ensure_ascii=False)

                dialogue_rounds = data.get("对话模式", {}).get("对话轮次", 0)
                start_time = data.get("对话模式", {}).get("会话开始时间", "")
                end_time = data.get("对话模式", {}).get("会话结束时间", "")
                # duration 需要转换为 int
                duration = int(data.get("对话模式", {}).get("会话持续时长", 0))
                service_efficiency = data.get("对话模式", {}).get("客服处理效率", "")
                efficiency_reason = data.get("对话模式", {}).get("理由", "")

                complaint_type = data.get("风险点识别", {}).get("投诉类型", "")
                risk_level = data.get("风险点识别", {}).get("投诉风险", "")
                risk_evidence = json.dumps(data.get("风险点识别", {}).get("佐证", []), ensure_ascii=False)

                resolution_result = data.get("解决结果", {}).get("客服处理结果", "")

                rule_match = data.get("规则判断", {}).get("规则命中", "")
                rule_level = data.get("规则判断", {}).get("规则命中等级", "")

                params_list.append((
                    session_id, user_demand, demand_evidence, keyword_match, keywords,
                    emotion_index, emotion_type, emotion_intensity, emotion_evidence,
                    dialogue_rounds, start_time, end_time, duration, service_efficiency, efficiency_reason,
                    complaint_type, risk_level, risk_evidence, resolution_result,
                    rule_match, rule_level
                ))

            return self._execute_batch_query(
                """
                INSERT INTO user_complaints (session_id, user_demand, demand_evidence, keyword_match, keywords,
                 emotion_index, emotion_type, emotion_intensity, emotion_evidence,
                 dialogue_rounds, start_time, end_time, duration, service_efficiency, efficiency_reason,
                 complaint_type, risk_level, risk_evidence, resolution_result,
                 rule_match, rule_level) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                params_list
            )
        except Exception as e:
            print(f"[ERROR] 批量存储投诉数据失败: {e}")
            return False

    def _store_session_data(self, data: Dict[str, Any]) -> bool:
        """存储会话数据（单条）"""
        try:
            # 处理特殊字段
            custom_filed = json.dumps(data.get('customFiled', [])) if isinstance(data.get('customFiled'), list) else '[]'
            satisfaction_tags = json.dumps(data.get('satisfactionTags', [])) if isinstance(data.get('satisfactionTags'), list) else None
            video_record_url_list = json.dumps(data.get('videoRecordUrlList', [])) if isinstance(data.get('videoRecordUrlList'), list) else None
            
            # 确保数值类型字段不为 None
            def ensure_int(value, default=0):
                try:
                    if value is None:
                        return default
                    return int(value)
                except (ValueError, TypeError):
                    return default
                    
            def ensure_bigint(value, default=0):
                try:
                    if value is None:
                        return default
                    return int(value)
                except (ValueError, TypeError):
                    return default

            query = """
            INSERT INTO sessions (
                id, startTime, endTime, sType, treatedTime, category, categoryDetail,
                evaluation, evaluationType, evaluationRemark, staffInvitedEvaluateTime,
                userJoinEvaluateTime, relatedType, relatedId, interaction, closeReason,
                fromGroup, fromGroupId, fromStaff, inQueueTime, visitRange, vipLevel,
                staffId, staffName, userId, foreignId, fromIp, fromPage, fromTitle,
                fromType, fromSubType, customFiled, description, transferRgType,
                fromHumanTransfer, transferFromStaffName, transferFromGroup,
                transferRemarks, humanTransferSessionId, worksheetIds, roundNumber,
                status, userResolvedStatus, firstReplyCost, avgRespDuration, isValid,
                transferType, staffMessageCount, userMessageCount, overflowFrom,
                alarmTimes, staffFirstReplyTime, stickDuration, clientFirstMessageTime,
                isEvaluationInvited, beginer, ender, originPlatform, searchKey,
                landPage, satisfactionTags, satisfyMsgCount, createTime, queueDuration,
                sessionDuration, visitTimes, staffAccount, categoryId, overflowRuleName,
                overflowCondition, startReason, userIpCity, staffReceptionDuration,
                videoRecordUrlList, preQueue, externalUserId, externalEscrowId, userCrmInfo
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s)
            """
            
            params = (
                ensure_bigint(data.get('id')),                           # id
                ensure_bigint(data.get('startTime')),                    # startTime
                ensure_bigint(data.get('endTime')),                      # endTime
                ensure_int(data.get('sType')),                          # sType
                ensure_bigint(data.get('treatedTime')),                  # treatedTime
                data.get('category', ''),                               # category
                data.get('categoryDetail', ''),                         # categoryDetail
                ensure_int(data.get('evaluation')),                     # evaluation
                ensure_bigint(data.get('evaluationType')),               # evaluationType
                data.get('evaluationRemark', ''),                       # evaluationRemark
                ensure_bigint(data.get('staffInvitedEvaluateTime')),     # staffInvitedEvaluateTime
                ensure_bigint(data.get('userJoinEvaluateTime')),         # userJoinEvaluateTime
                ensure_int(data.get('relatedType')),                    # relatedType
                ensure_bigint(data.get('relatedId')),                    # relatedId
                ensure_int(data.get('interaction')),                    # interaction
                ensure_int(data.get('closeReason')),                    # closeReason
                data.get('fromGroup', ''),                              # fromGroup
                ensure_bigint(data.get('fromGroupId')),                  # fromGroupId
                data.get('fromStaff', ''),                              # fromStaff
                ensure_bigint(data.get('inQueueTime')),                  # inQueueTime
                ensure_bigint(data.get('visitRange')),                   # visitRange
                ensure_int(data.get('vipLevel')),                       # vipLevel
                ensure_bigint(data.get('staffId')),                      # staffId
                data.get('staffName', ''),                              # staffName
                str(data.get('userId', '')),                            # userId (改为 varchar)
                data.get('foreignId', ''),                              # foreignId
                data.get('fromIp', ''),                                 # fromIp
                data.get('fromPage', ''),                               # fromPage
                data.get('fromTitle', ''),                              # fromTitle
                data.get('fromType', ''),                               # fromType
                data.get('fromSubType', ''),                            # fromSubType
                custom_filed,                                           # customFiled
                data.get('description', ''),                            # description
                data.get('transferRgType', ''),                         # transferRgType
                ensure_int(data.get('fromHumanTransfer')),              # fromHumanTransfer
                data.get('transferFromStaffName', ''),                  # transferFromStaffName
                data.get('transferFromGroup', ''),                      # transferFromGroup
                data.get('transferRemarks', ''),                        # transferRemarks
                ensure_bigint(data.get('humanTransferSessionId')),       # humanTransferSessionId
                data.get('worksheetIds', ''),                           # worksheetIds
                ensure_bigint(data.get('roundNumber')),                  # roundNumber
                ensure_int(data.get('status')),                         # status
                ensure_int(data.get('userResolvedStatus')),             # userResolvedStatus
                ensure_bigint(data.get('firstReplyCost')),              # firstReplyCost
                ensure_bigint(data.get('avgRespDuration')),             # avgRespDuration
                ensure_int(data.get('isValid')),                        # isValid
                data.get('transferType', ''),                           # transferType
                ensure_int(data.get('staffMessageCount')),              # staffMessageCount
                ensure_int(data.get('userMessageCount')),               # userMessageCount
                data.get('overflowFrom', ''),                           # overflowFrom
                ensure_int(data.get('alarmTimes')),                     # alarmTimes
                ensure_bigint(data.get('staffFirstReplyTime')),         # staffFirstReplyTime
                ensure_bigint(data.get('stickDuration')),               # stickDuration
                ensure_bigint(data.get('clientFirstMessageTime')),       # clientFirstMessageTime
                ensure_int(data.get('isEvaluationInvited')),           # isEvaluationInvited
                ensure_int(data.get('beginer')),                        # beginer
                ensure_int(data.get('ender')),                          # ender
                data.get('originPlatform', ''),                         # originPlatform
                data.get('searchKey', ''),                              # searchKey
                data.get('landPage', ''),                               # landPage
                satisfaction_tags,                                      # satisfactionTags
                ensure_int(data.get('satisfyMsgCount')),               # satisfyMsgCount
                data.get('createTime', ''),                             # createTime
                data.get('queueDuration', ''),                          # queueDuration
                data.get('sessionDuration', ''),                        # sessionDuration
                data.get('visitTimes', ''),                             # visitTimes
                data.get('staffAccount', ''),                           # staffAccount
                ensure_bigint(data.get('categoryId')),                  # categoryId
                data.get('overflowRuleName', ''),                       # overflowRuleName
                data.get('overflowCondition', ''),                      # overflowCondition
                data.get('startReason', ''),                            # startReason
                data.get('userIpCity', ''),                             # userIpCity
                data.get('staffReceptionDuration', ''),                 # staffReceptionDuration
                video_record_url_list,                                  # videoRecordUrlList
                ensure_int(data.get('preQueue')),                       # preQueue
                data.get('externalUserId', ''),                         # externalUserId
                data.get('externalEscrowId', ''),                        # externalEscrowId
                data.get('userCrmInfo', '')                             # userCrmInfo
            )
            
            return self._execute_query(query, params)
        except Exception as e:
            print(f"[ERROR] 存储会话数据失败: {e}")
            return False
            
    def _batch_store_session_data(self, data_list: List[Dict[str, Any]]) -> bool:
        """批量存储会话数据"""
        try:
            params_list = []
            for data in data_list:
                # 处理特殊字段
                custom_filed = json.dumps(data.get('customFiled', [])) if isinstance(data.get('customFiled'), list) else '[]'
                satisfaction_tags = json.dumps(data.get('satisfactionTags', [])) if isinstance(data.get('satisfactionTags'), list) else None
                video_record_url_list = json.dumps(data.get('videoRecordUrlList', [])) if isinstance(data.get('videoRecordUrlList'), list) else None
                
                # 确保数值类型字段不为 None
                def ensure_int(value, default=0):
                    try:
                        if value is None:
                            return default
                        return int(value)
                    except (ValueError, TypeError):
                        return default
                        
                def ensure_bigint(value, default=0):
                    try:
                        if value is None:
                            return default
                        return int(value)
                    except (ValueError, TypeError):
                        return default

                params_list.append((
                    ensure_bigint(data.get('id')),                           # id
                    ensure_bigint(data.get('startTime')),                    # startTime
                    ensure_bigint(data.get('endTime')),                      # endTime
                    ensure_int(data.get('sType')),                          # sType
                    ensure_bigint(data.get('treatedTime')),                  # treatedTime
                    data.get('category', ''),                               # category
                    data.get('categoryDetail', ''),                         # categoryDetail
                    ensure_int(data.get('evaluation')),                     # evaluation
                    ensure_bigint(data.get('evaluationType')),               # evaluationType
                    data.get('evaluationRemark', ''),                       # evaluationRemark
                    ensure_bigint(data.get('staffInvitedEvaluateTime')),     # staffInvitedEvaluateTime
                    ensure_bigint(data.get('userJoinEvaluateTime')),         # userJoinEvaluateTime
                    ensure_int(data.get('relatedType')),                    # relatedType
                    ensure_bigint(data.get('relatedId')),                    # relatedId
                    ensure_int(data.get('interaction')),                    # interaction
                    ensure_int(data.get('closeReason')),                    # closeReason
                    data.get('fromGroup', ''),                              # fromGroup
                    ensure_bigint(data.get('fromGroupId')),                  # fromGroupId
                    data.get('fromStaff', ''),                              # fromStaff
                    ensure_bigint(data.get('inQueueTime')),                  # inQueueTime
                    ensure_bigint(data.get('visitRange')),                   # visitRange
                    ensure_int(data.get('vipLevel')),                       # vipLevel
                    ensure_bigint(data.get('staffId')),                      # staffId
                    data.get('staffName', ''),                              # staffName
                    str(data.get('userId', '')),                            # userId (改为 varchar)
                    data.get('foreignId', ''),                              # foreignId
                    data.get('fromIp', ''),                                 # fromIp
                    data.get('fromPage', ''),                               # fromPage
                    data.get('fromTitle', ''),                              # fromTitle
                    data.get('fromType', ''),                               # fromType
                    data.get('fromSubType', ''),                            # fromSubType
                    custom_filed,                                           # customFiled
                    data.get('description', ''),                            # description
                    data.get('transferRgType', ''),                         # transferRgType
                    ensure_int(data.get('fromHumanTransfer')),              # fromHumanTransfer
                    data.get('transferFromStaffName', ''),                  # transferFromStaffName
                    data.get('transferFromGroup', ''),                      # transferFromGroup
                    data.get('transferRemarks', ''),                        # transferRemarks
                    ensure_bigint(data.get('humanTransferSessionId')),       # humanTransferSessionId
                    data.get('worksheetIds', ''),                           # worksheetIds
                    ensure_bigint(data.get('roundNumber')),                  # roundNumber
                    ensure_int(data.get('status')),                         # status
                    ensure_int(data.get('userResolvedStatus')),             # userResolvedStatus
                    ensure_bigint(data.get('firstReplyCost')),              # firstReplyCost
                    ensure_bigint(data.get('avgRespDuration')),             # avgRespDuration
                    ensure_int(data.get('isValid')),                        # isValid
                    data.get('transferType', ''),                           # transferType
                    ensure_int(data.get('staffMessageCount')),              # staffMessageCount
                    ensure_int(data.get('userMessageCount')),               # userMessageCount
                    data.get('overflowFrom', ''),                           # overflowFrom
                    ensure_int(data.get('alarmTimes')),                     # alarmTimes
                    ensure_bigint(data.get('staffFirstReplyTime')),         # staffFirstReplyTime
                    ensure_bigint(data.get('stickDuration')),               # stickDuration
                    ensure_bigint(data.get('clientFirstMessageTime')),       # clientFirstMessageTime
                    ensure_int(data.get('isEvaluationInvited')),           # isEvaluationInvited
                    ensure_int(data.get('beginer')),                        # beginer
                    ensure_int(data.get('ender')),                          # ender
                    data.get('originPlatform', ''),                         # originPlatform
                    data.get('searchKey', ''),                              # searchKey
                    data.get('landPage', ''),                               # landPage
                    satisfaction_tags,                                      # satisfactionTags
                    ensure_int(data.get('satisfyMsgCount')),               # satisfyMsgCount
                    data.get('createTime', ''),                             # createTime
                    data.get('queueDuration', ''),                          # queueDuration
                    data.get('sessionDuration', ''),                        # sessionDuration
                    data.get('visitTimes', ''),                             # visitTimes
                    data.get('staffAccount', ''),                           # staffAccount
                    ensure_bigint(data.get('categoryId')),                  # categoryId
                    data.get('overflowRuleName', ''),                       # overflowRuleName
                    data.get('overflowCondition', ''),                      # overflowCondition
                    data.get('startReason', ''),                            # startReason
                    data.get('userIpCity', ''),                             # userIpCity
                    data.get('staffReceptionDuration', ''),                 # staffReceptionDuration
                    video_record_url_list,                                  # videoRecordUrlList
                    ensure_int(data.get('preQueue')),                       # preQueue
                    data.get('externalUserId', ''),                         # externalUserId
                    data.get('externalEscrowId', ''),                        # externalEscrowId
                    data.get('userCrmInfo', '')                             # userCrmInfo
                ))

            query = """
            INSERT INTO sessions (
                id, startTime, endTime, sType, treatedTime, category, categoryDetail,
                evaluation, evaluationType, evaluationRemark, staffInvitedEvaluateTime,
                userJoinEvaluateTime, relatedType, relatedId, interaction, closeReason,
                fromGroup, fromGroupId, fromStaff, inQueueTime, visitRange, vipLevel,
                staffId, staffName, userId, foreignId, fromIp, fromPage, fromTitle,
                fromType, fromSubType, customFiled, description, transferRgType,
                fromHumanTransfer, transferFromStaffName, transferFromGroup,
                transferRemarks, humanTransferSessionId, worksheetIds, roundNumber,
                status, userResolvedStatus, firstReplyCost, avgRespDuration, isValid,
                transferType, staffMessageCount, userMessageCount, overflowFrom,
                alarmTimes, staffFirstReplyTime, stickDuration, clientFirstMessageTime,
                isEvaluationInvited, beginer, ender, originPlatform, searchKey,
                landPage, satisfactionTags, satisfyMsgCount, createTime, queueDuration,
                sessionDuration, visitTimes, staffAccount, categoryId, overflowRuleName,
                overflowCondition, startReason, userIpCity, staffReceptionDuration,
                videoRecordUrlList, preQueue, externalUserId, externalEscrowId, userCrmInfo
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s)
            """
            
            return self._execute_batch_query(query, params_list)
        except Exception as e:
            print(f"[ERROR] 批量存储会话数据失败: {e}")
            return False

    def _store_message_data(self, data: Dict[str, Any]) -> bool:
        """存储消息数据（单条）"""
        try:
            query = """
            INSERT INTO messages (
                id, sessionId, staffId, staffName, userId, userName, `from`, time,
                mType, autoReply, msg, status, isRichMedia, evaluation,
                msgEvaluationContent, matchType, matchKnowledgeId, externalUserId,
                escrowAccount
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s)
            """
            params = tuple(data.get(field, None) for field in [
                'id', 'sessionId', 'staffId', 'staffName', 'userId', 'userName',
                'from', 'time', 'mType', 'autoReply', 'msg', 'status', 'isRichMedia',
                'evaluation', 'msgEvaluationContent', 'matchType', 'matchKnowledgeId',
                'externalUserId', 'escrowAccount'
            ])
            return self._execute_query(query, params)
        except Exception as e:
            print(f"[ERROR] 存储消息数据失败: {e}")
            return False
            
    def _batch_store_message_data(self, data_list: List[Dict[str, Any]]) -> bool:
        """批量存储消息数据"""
        try:
            params_list = []
            for data in data_list:
                params_list.append(tuple(data.get(field, None) for field in [
                    'id', 'sessionId', 'staffId', 'staffName', 'userId', 'userName',
                    'from', 'time', 'mType', 'autoReply', 'msg', 'status', 'isRichMedia',
                    'evaluation', 'msgEvaluationContent', 'matchType', 'matchKnowledgeId',
                    'externalUserId', 'escrowAccount'
                ]))
                
            query = """
            INSERT INTO messages (
                id, sessionId, staffId, staffName, userId, userName, `from`, time,
                mType, autoReply, msg, status, isRichMedia, evaluation,
                msgEvaluationContent, matchType, matchKnowledgeId, externalUserId,
                escrowAccount
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s)
            """
            
            return self._execute_batch_query(query, params_list)
        except Exception as e:
            print(f"[ERROR] 批量存储消息数据失败: {e}")
            return False

    def _store_conversation_data(self, data: Dict[str, Any]) -> bool:
        """存储对话数据（单条）"""
        try:
            query = """
            INSERT INTO conversations (
                session_id, conversation_text, staff_name, user_name, member_id
            ) VALUES (%s, %s, %s, %s, %s)
            """
            
            # 确保 member_id 为 bigint 类型
            try:
                member_id = int(data.get('member_id', 0))
            except (ValueError, TypeError):
                member_id = 0
                
            params = (
                data.get('session_id'),
                data.get('conversation_text'),
                data.get('staff_name'),
                data.get('user_name'),
                member_id
            )
            return self._execute_query(query, params)
        except Exception as e:
            print(f"[ERROR] 存储对话数据失败: {e}")
            return False
            
    def _batch_store_conversation_data(self, data_list: List[Dict[str, Any]]) -> bool:
        """批量存储对话数据"""
        try:
            params_list = []
            for data in data_list:
                # 确保 member_id 为 bigint 类型
                try:
                    member_id = int(data.get('member_id', 0))
                except (ValueError, TypeError):
                    member_id = 0
                    
                params_list.append((
                    data.get('session_id'),
                    data.get('conversation_text'),
                    data.get('staff_name'),
                    data.get('user_name'),
                    member_id
                ))
                
            query = """
            INSERT INTO conversations (
                session_id, conversation_text, staff_name, user_name, member_id
            ) VALUES (%s, %s, %s, %s, %s)
            """
            
            return self._execute_batch_query(query, params_list)
        except Exception as e:
            print(f"[ERROR] 批量存储对话数据失败: {e}")
            return False

    # 公共接口方法
    def store_complaint(self, session_id: str, analysis_data: Dict[str, Any]):
        """异步存储投诉分析数据"""
        self.data_queue.put({
            'table': 'user_complaints',
            'method': 'store_complaint',
            'data': {
                'session_id': session_id,
                'analysis_data': analysis_data
            }
        })

    def store_session(self, session_data: Dict[str, Any]):
        """异步存储会话数据"""
        self.data_queue.put({
            'table': 'sessions',
            'method': 'store_session',
            'data': session_data
        })

    def store_message(self, message_data: Dict[str, Any]):
        """异步存储消息数据"""
        self.data_queue.put({
            'table': 'messages',
            'method': 'store_message',
            'data': message_data
        })

    def store_conversation(self, conversation_data: Dict[str, Any]):
        """异步存储对话数据"""
        self.data_queue.put({
            'table': 'conversations',
            'method': 'store_conversation',
            'data': conversation_data
        })
        
    def store_qa_pair_async(self, qa_data: Dict[str, Any]):
        """异步存储QA对数据"""
        self.data_queue.put({
            'table': 'qa_pairs',
            'method': 'store_qa_pair',
            'data': qa_data
        })

    def store_structured_conversation(self, conversation_data: Dict[str, Any]):
        """异步存储结构化会话数据"""
        self.data_queue.put({
            'table': 'structured_conversations',
            'method': 'store_structured_conversation',
            'data': conversation_data
        })

    def get_complete_session_messages(self, session_id):
        """获取指定会话ID的所有消息记录
        
        Args:
            session_id: 会话ID
            
        Returns:
            list: 消息记录列表，按时间排序
        """
        try:
            print(f"[DEBUG-跨窗口] 正在从数据库查询会话 {session_id} 的完整消息记录")

            query = f"""
            SELECT * FROM messages
            WHERE staffId > 0 and sessionId = %s
            ORDER BY time ASC
            """

            print(f"[DEBUG-跨窗口] 执行SQL: {query.strip()} [参数: {session_id}]")

            # 使用 pymysql.cursors.DictCursor 代替 dictionary=True
            from pymysql.cursors import DictCursor
            cursor = self.conn.cursor(DictCursor)
            cursor.execute(query, (session_id,))
            messages = cursor.fetchall()
            cursor.close()

            if messages:
                # 统计消息时间范围
                times = [msg.get('time', 0) for msg in messages if msg.get('time')]
                if times:
                    min_time = min(times)
                    max_time = max(times)
                    print(f"[DEBUG-跨窗口] 查询结果: 找到 {len(messages)} 条消息，时间范围: "
                          f"{datetime.fromtimestamp(min_time/1000)} -> {datetime.fromtimestamp(max_time/1000)}")
                else:
                    print(f"[DEBUG-跨窗口] 查询结果: 找到 {len(messages)} 条消息，但没有有效的时间戳")
            else:
                print(f"[DEBUG-跨窗口] 查询结果: 未找到任何消息记录")
            
            return messages
        except Exception as e:
            print(f"[ERROR] 从数据库获取会话 {session_id} 的消息记录失败: {e}")
            return []

    # 新增值班人员相关方法
    def get_active_duty_staff(self) -> List[Dict[str, Any]]:
        """获取当前值班人员列表"""
        try:
            query = """
            SELECT id, staff_name, open_id 
            FROM duty_staff 
            WHERE is_active = 1
            """
            
            if not self._ensure_connection():
                return []
                
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            
            staff_list = []
            for row in results:
                staff_list.append({
                    'id': row[0],
                    'staff_name': row[1],
                    'open_id': row[2]
                })
                
            print(f"[INFO] 获取到 {len(staff_list)} 名值班人员")
            return staff_list
        except Exception as e:
            print(f"[ERROR] 获取值班人员失败: {e}")
            return []
    
    def create_alert_task(self, session_id: str, alert_type: str, alert_level: str, 
                         alert_content: str, assigned_staff: str, open_id: str, feishu_task_id: str) -> bool:
        """创建告警任务"""
        try:
            query = """
            INSERT INTO alert_tasks (
                session_id, alert_type, alert_level, alert_content, 
                assigned_staff, assigned_open_id, feishu_task_id, task_status
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, 'pending')
            """
            
            params = (
                session_id, alert_type, alert_level, alert_content, 
                assigned_staff, open_id, feishu_task_id
            )
            
            if not self._ensure_connection():
                return False
                
            self.cursor.execute(query, params)
            self.conn.commit()
            
            print(f"[INFO] 成功创建告警任务: {session_id} 分配给 {assigned_staff}")
            return True
        except Exception as e:
            print(f"[ERROR] 创建告警任务失败: {e}")
            return False
    
    def get_all_pending_tasks(self) -> List[Dict[str, Any]]:
        """获取所有超时待处理任务
        
        只返回创建时间超过 TASK_REMINDER_THRESHOLD 分钟的任务
        """
        try:
            # 计算阈值时间（当前时间减去阈值分钟数）
            threshold_time = datetime.now() - timedelta(minutes=config.TASK_REMINDER_THRESHOLD)
            threshold_time_str = threshold_time.strftime('%Y-%m-%d %H:%M:%S')
            
            query = """
            SELECT session_id, alert_type, alert_level, alert_content, create_time, assigned_staff, assigned_open_id, feishu_task_id, task_status,
                   TIMESTAMPDIFF(MINUTE, create_time, NOW()) as time_diff_minutes
            FROM alert_tasks
            WHERE task_status = 'pending'
              AND create_time <= %s
            ORDER BY assigned_staff, create_time ASC
            """
            
            if not self._ensure_connection():
                return []
                
            self.cursor.execute(query, (threshold_time_str,))
            results = self.cursor.fetchall()
            
            tasks = []
            for row in results:
                tasks.append({
                    'session_id': row[0],
                    'alert_type': row[1],
                    'alert_level': row[2],
                    'alert_content': row[3],
                    'create_time': row[4],
                    'assigned_staff': row[5],
                    'assigned_open_id': row[6],
                    'feishu_task_id': row[7],
                    'task_status': row[8],
                    'time_diff_minutes': row[9]
                })
                
            return tasks
        except Exception as e:
            print(f"[ERROR] 获取所有待处理任务失败: {e}")
            return []
    
    def update_task_status(self, feishu_task_id: str, status: str) -> bool:
        """更新任务状态
        
        Args:
            feishu_task_id (str): 飞书任务ID
            status (str): 新的任务状态
            
        Returns:
            bool: 更新是否成功
        """
        try:
            query = """
            UPDATE alert_tasks
            SET task_status = %s, update_time = NOW()
            WHERE feishu_task_id = %s
            """
            
            if not self._ensure_connection():
                print(f"[ERROR] 无法建立数据库连接，更新任务 {feishu_task_id} 状态失败")
                return False
                
            self.cursor.execute(query, (status, feishu_task_id))
            self.conn.commit()
            
            # 检查是否有行被更新
            if self.cursor.rowcount > 0:
                print(f"[INFO] 成功更新任务 {feishu_task_id} 状态为 {status}")
                return True
            else:
                print(f"[WARN] 未找到任务 {feishu_task_id}, 无法更新状态")
                return False
        except Exception as e:
            print(f"[ERROR] 更新任务状态失败: {e}")
            # 不要在这里断开连接，让调用者决定何时断开连接
            return False
    
    def update_feishu_task_id(self, session_id: str, assigned_staff: str, feishu_task_id: str) -> bool:
        """更新任务的飞书任务ID
        
        Args:
            session_id (str): 会话ID
            assigned_staff (str): 分配的值班人员
            feishu_task_id (str): 飞书任务ID
            
        Returns:
            bool: 更新是否成功
        """
        try:
            query = """
            UPDATE alert_tasks
            SET feishu_task_id = %s, update_time = NOW()
            WHERE session_id = %s AND assigned_staff = %s AND task_status = 'pending'
            """
            
            if not self._ensure_connection():
                return False
                
            self.cursor.execute(query, (feishu_task_id, session_id, assigned_staff))
            self.conn.commit()
            
            # 检查是否有行被更新
            if self.cursor.rowcount > 0:
                print(f"[INFO] 成功更新会话 {session_id} 分配给 {assigned_staff} 的任务飞书ID为 {feishu_task_id}")
                return True
            else:
                print(f"[WARN] 未找到会话 {session_id} 分配给 {assigned_staff} 的待处理任务，无法更新飞书任务ID")
                return False
                
        except Exception as e:
            print(f"[ERROR] 更新任务飞书ID失败: {e}")
            return False
        
    def store_qa_to_sr(self, qa_data: Dict[str, Any]):
        """存储QA对到StarRocks数据库"""
        print(f"存储QA对到StarRocks数据库: {qa_data}")
        return True

    def store_qa_pair(self, qa_data: Dict[str, Any]) -> bool:
        """存储QA对数据到StarRocks数据库
        
        Args:
            qa_data: QA对数据字典，包含以下字段：
                - session_id: 会话ID
                - session_id_q: 该会话的第几个问题
                - catagory_1: 一级分类
                - catagory_2: 二级分类
                - catagory_3: 三级分类
                - standard_question: 标准问题
                - similar_question: 相似问题
                - question_relevant: 问题相关会话记录
                - standard_answer: 标准答案
                - answer_relevant: 答案相关会话记录
                
        Returns:
            bool: 存储是否成功
        """
        try:
            query = """
            INSERT INTO question_pairs (
                session_id, session_id_q, catagory_1, catagory_2, catagory_3,
                standard_question, similar_question, question_relevant,
                standard_answer, answer_relevant
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            params = (
                qa_data.get('session_id', ''),
                qa_data.get('session_id_q', 1),
                qa_data.get('catagory_1', ''),
                qa_data.get('catagory_2', ''),
                qa_data.get('catagory_3', ''),
                qa_data.get('standard_question', ''),
                qa_data.get('similar_question', ''),
                qa_data.get('question_relevant', ''),
                qa_data.get('standard_answer', ''),
                qa_data.get('answer_relevant', '')
            )
            
            return self._execute_query(query, params)
            
        except Exception as e:
            print(f"[ERROR] 存储QA对数据失败: {e}")
            return False

    def _batch_store_qa_pair_data(self, data_list: List[Dict[str, Any]]) -> bool:
        """批量存储QA对数据到StarRocks数据库
        
        Args:
            data_list: QA对数据列表
            
        Returns:
            bool: 存储是否成功
        """
        try:
            params_list = []
            for qa_data in data_list:
                params_list.append((
                    qa_data.get('session_id', ''),
                    qa_data.get('session_id_q', 1),
                    qa_data.get('catagory_1', ''),
                    qa_data.get('catagory_2', ''),
                    qa_data.get('catagory_3', ''),
                    qa_data.get('standard_question', ''),
                    qa_data.get('similar_question', ''),
                    qa_data.get('question_relevant', ''),
                    qa_data.get('standard_answer', ''),
                    qa_data.get('answer_relevant', '')
                ))
            
            query = """
            INSERT INTO question_pairs (
                session_id, session_id_q, catagory_1, catagory_2, catagory_3,
                standard_question, similar_question, question_relevant,
                standard_answer, answer_relevant
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            return self._execute_batch_query(query, params_list)
            
        except Exception as e:
            print(f"[ERROR] 批量存储QA对数据失败: {e}")
            return False

    def _batch_store_structured_conversation_data(self, data_list: List[Dict[str, Any]]) -> bool:
        """批量存储结构化会话数据到StarRocks数据库
        
        Args:
            data_list: 结构化会话数据列表
            
        Returns:
            bool: 存储是否成功
        """
        try:
            params_list = []
            for data in data_list:
                params_list.append((
                    data.get('session_id', ''),
                    data.get('staff_id', 0),
                    data.get('staff_name', ''),
                    data.get('user_id', ''),
                    data.get('user_name', ''),
                    data.get('member_id', 0),
                    data.get('create_time'),
                    data.get('end_time'),
                    data.get('conversation_content', ''),
                    data.get('message_count', 0),
                    data.get('is_processed', True),
                    data.get('processed_time'),
                    data.get('run_id', ''),
                    data.get('created_at'),
                    data.get('updated_at')
                ))
            
            query = """
            INSERT INTO test.structured_conversations (
                session_id, staff_id, staff_name, user_id, user_name, member_id,
                create_time, end_time, conversation_content, message_count,
                is_processed, processed_time, run_id, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            return self._execute_batch_query(query, params_list)
        except Exception as e:
            print(f"[ERROR] 批量存储结构化会话数据失败: {e}")
            return False