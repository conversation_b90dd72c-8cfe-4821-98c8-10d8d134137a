#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import time
import argparse
from typing import Dict, List, Any, Tuple, Set
from collections import defaultdict
from qdrant_client import QdrantClient
from openai import AsyncAzureOpenAI
import numpy as np

# 添加StarRocks相关导入
import config
from store_data_to_sr import StarRocksStore

class QARepairTool:
    """QA数据修复工具：处理并发导致的重复问题（同时修复Qdrant和StarRocks）"""
    
    def __init__(self, similarity_threshold: float = 0.85, batch_size: int = 100):
        self.client = QdrantClient(host="localhost", port=6333)
        
        self.emb_client = AsyncAzureOpenAI(
            api_key="********************************",
            api_version="2024-05-01-preview",
            azure_endpoint="https://portai-eastus2.openai.azure.com/"
        )
        
        # 初始化StarRocks连接
        self.db = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE,
            batch_size=batch_size
        )
        
        self.similarity_threshold = similarity_threshold
        self.batch_size = batch_size
        
        print(f"[INFO] QA修复工具初始化完成")
        print(f"[INFO] Qdrant相似度阈值: {similarity_threshold}")
        print(f"[INFO] StarRocks数据库: {config.STARROCKS_HOST}:{config.STARROCKS_PORT}")
    
    async def find_duplicate_questions(self) -> List[List[Dict[str, Any]]]:
        """查找所有重复的标准问题"""
        
        print("[INFO] 开始扫描重复问题...")
        
        # 获取所有需要检查的问题
        all_points = []
        offset = None
        
        while True:
            try:
                result = self.client.scroll(
                    collection_name="qa_questions",
                    limit=self.batch_size,
                    offset=offset,
                    with_payload=True,
                    with_vectors=True
                )
                
                points, next_offset = result
                
                if not points:  # 没有更多数据
                    break
                    
                all_points.extend(points)
                offset = next_offset
                
                print(f"[INFO] 已扫描 {len(all_points)} 个问题...")
                
                if next_offset is None:  # 最后一批
                    break
                    
            except Exception as e:
                print(f"[ERROR] 扫描问题时出错: {e}")
                break
        
        print(f"[INFO] 共扫描到 {len(all_points)} 个问题")
        
        if not all_points:
            print("[INFO] 未找到任何问题数据")
            return []
        
        # 按相似度分组
        duplicate_groups = await self._group_similar_questions(all_points)
        
        print(f"[INFO] 发现 {len(duplicate_groups)} 组重复问题")
        return duplicate_groups
    
    async def _group_similar_questions(self, points: List[Any]) -> List[List[Dict[str, Any]]]:
        """将相似的问题分组"""
        
        print("[INFO] 开始分析问题相似度...")
        
        # 构建问题数据
        questions_data = []
        for point in points:
            question_text = point.payload.get('标准问题', '') if point.payload else ''
            if question_text:  # 只处理有效问题
                questions_data.append({
                    'id': point.id,
                    'vector': point.vector if hasattr(point, 'vector') else None,
                    'payload': point.payload,
                    'question': question_text,
                    'processed': False
                })
        
        print(f"[INFO] 有效问题数量: {len(questions_data)}")
        
        duplicate_groups = []
        processed_count = 0
        
        for i, question_data in enumerate(questions_data):
            if question_data['processed']:
                continue
                
            # 创建新组
            current_group = [question_data]
            question_data['processed'] = True
            processed_count += 1
            
            # 查找相似的问题
            for j, other_question_data in enumerate(questions_data[i+1:], i+1):
                if other_question_data['processed']:
                    continue
                
                # 计算相似度
                similarity = self._calculate_similarity(question_data, other_question_data)
                
                if similarity >= self.similarity_threshold:
                    current_group.append(other_question_data)
                    other_question_data['processed'] = True
                    processed_count += 1
            
            # 只有找到重复的才加入结果
            if len(current_group) > 1:
                duplicate_groups.append(current_group)
                print(f"[INFO] 发现重复组 {len(duplicate_groups)}: {len(current_group)} 个相似问题")
            
            # 进度提示
            if (i + 1) % 100 == 0:
                print(f"[INFO] 处理进度: {i + 1}/{len(questions_data)}")
                
        print(f"[INFO] 相似度分析完成，处理了 {processed_count} 个问题")
        return duplicate_groups
    
    def _calculate_similarity(self, question1: Dict[str, Any], question2: Dict[str, Any]) -> float:
        """计算两个问题的相似度"""
        
        # 方法1: 如果有向量，使用向量相似度
        if (question1.get('vector') is not None and 
            question2.get('vector') is not None):
            return self._cosine_similarity(question1['vector'], question2['vector'])
        
        # 方法2: 使用文本相似度（简单版本）
        text1 = question1['question']
        text2 = question2['question']
        
        # 简单的文本相似度计算
        return self._text_similarity(text1, text2)
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
                
            return float(dot_product / (norm1 * norm2))
        except Exception as e:
            print(f"[WARNING] 计算余弦相似度时出错: {e}")
            return 0.0
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """简单的文本相似度计算"""
        
        # 基于字符的简单相似度
        if text1 == text2:
            return 1.0
        
        # 计算最长公共子序列长度
        def lcs_length(s1, s2):
            m, n = len(s1), len(s2)
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if s1[i-1] == s2[j-1]:
                        dp[i][j] = dp[i-1][j-1] + 1
                    else:
                        dp[i][j] = max(dp[i-1][j], dp[i][j-1])
            
            return dp[m][n]
        
        lcs_len = lcs_length(text1, text2)
        max_len = max(len(text1), len(text2))
        
        if max_len == 0:
            return 1.0
            
        return lcs_len / max_len

    def _find_starrocks_duplicates(self, questions: List[str]) -> List[Dict[str, Any]]:
        """在StarRocks中查找重复的QA记录"""
        try:
            if not self.db._ensure_connection():
                print("[ERROR] 无法连接到StarRocks数据库")
                return []
            
            # 构建SQL查询，查找这些问题的所有记录
            placeholders = ','.join(['%s'] * len(questions))
            query = f"""
            SELECT session_id, session_id_q, standard_question, similar_question, 
                   catagory_1, catagory_2, catagory_3, standard_answer
            FROM qa_pairs 
            WHERE standard_question IN ({placeholders}) 
               OR similar_question IN ({placeholders})
            ORDER BY standard_question, session_id, session_id_q
            """
            
            # 执行查询，questions参数需要重复两次（standard_question和similar_question各一次）
            self.db.cursor.execute(query, questions + questions)
            records = self.db.cursor.fetchall()
            
            # 转换为字典列表
            columns = ['session_id', 'session_id_q', 'standard_question', 'similar_question',
                      'catagory_1', 'catagory_2', 'catagory_3', 'standard_answer']
            
            result = []
            for record in records:
                record_dict = dict(zip(columns, record))
                result.append(record_dict)
            
            print(f"[INFO] 在StarRocks中找到 {len(result)} 条相关记录")
            return result
            
        except Exception as e:
            print(f"[ERROR] 查询StarRocks重复记录失败: {e}")
            return []

    def _delete_starrocks_duplicates(self, records_to_delete: List[Dict[str, Any]]) -> int:
        """删除StarRocks中的重复记录"""
        try:
            if not records_to_delete:
                return 0
                
            if not self.db._ensure_connection():
                print("[ERROR] 无法连接到StarRocks数据库")
                return 0
            
            deleted_count = 0
            
            # 按照session_id和session_id_q删除（更精确）
            for record in records_to_delete:
                try:
                    delete_query = """
                    DELETE FROM qa_pairs 
                    WHERE session_id = %s 
                      AND session_id_q = %s 
                      AND standard_question = %s
                      AND similar_question = %s
                    """
                    
                    self.db.cursor.execute(delete_query, (
                        record['session_id'],
                        record['session_id_q'],
                        record['standard_question'],
                        record['similar_question']
                    ))
                    
                    if self.db.cursor.rowcount > 0:
                        deleted_count += self.db.cursor.rowcount
                        
                except Exception as e:
                    print(f"[WARNING] 删除单条记录失败: {e}")
                    continue
            
            # 提交事务
            self.db.conn.commit()
            print(f"[INFO] 从StarRocks删除了 {deleted_count} 条重复记录")
            return deleted_count
            
        except Exception as e:
            print(f"[ERROR] 删除StarRocks重复记录失败: {e}")
            # 回滚事务
            if self.db.conn:
                self.db.conn.rollback()
            return 0

    async def repair_duplicate_groups(self, duplicate_groups: List[List[Dict[str, Any]]], dry_run: bool = False) -> Dict[str, int]:
        """修复重复问题组（同时修复Qdrant和StarRocks）"""
        
        repair_stats = {
            'groups_processed': 0,
            'questions_merged': 0,
            'questions_deleted': 0,
            'questions_kept': 0,
            'starrocks_records_deleted': 0
        }
        
        print(f"[INFO] {'预览' if dry_run else '开始修复'} {len(duplicate_groups)} 组重复问题")
        print(f"[INFO] 修复范围: Qdrant向量数据库 + StarRocks关系数据库")
        
        for group_index, group in enumerate(duplicate_groups, 1):
            print(f"\n[INFO] {'预览' if dry_run else '处理'}第 {group_index}/{len(duplicate_groups)} 组 (包含 {len(group)} 个重复问题)")
            
            # 选择主问题（保留哪一个）
            master_question = self._select_master_question(group)
            slave_questions = [q for q in group if q['id'] != master_question['id']]
            
            print(f"  主问题: \"{master_question['question']}\" (ID: {master_question['id']})")
            print(f"  待合并: {len(slave_questions)} 个问题")
            
            # 收集所有相关的问题文本，用于StarRocks查询
            all_question_texts = [q['question'] for q in group]
            
            # 查找StarRocks中的相关记录
            starrocks_records = self._find_starrocks_duplicates(all_question_texts)
            
            # 分析哪些StarRocks记录需要删除
            master_question_text = master_question['question']
            records_to_delete = []
            records_to_keep = []
            
            for record in starrocks_records:
                # 如果记录的标准问题或相似问题对应要删除的slave问题，则删除该记录
                is_slave_record = (
                    record['standard_question'] in [q['question'] for q in slave_questions] or
                    record['similar_question'] in [q['question'] for q in slave_questions]
                )
                
                if is_slave_record:
                    records_to_delete.append(record)
                else:
                    records_to_keep.append(record)
            
            print(f"  StarRocks记录: 总共{len(starrocks_records)}条, 保留{len(records_to_keep)}条, 删除{len(records_to_delete)}条")
            
            # 合并相似问题列表
            all_similar_questions = set(master_question['payload'].get('相似问题', []))
            
            for slave in slave_questions:
                slave_similar = slave['payload'].get('相似问题', [])
                all_similar_questions.update(slave_similar)
                print(f"    合并: \"{slave['question']}\" (ID: {slave['id']})")
            
            if not dry_run:
                # 1. 更新Qdrant主问题的相似问题列表
                try:
                    self.client.set_payload(
                        collection_name="qa_questions",
                        payload={"相似问题": list(all_similar_questions)},
                        points=[master_question['id']]
                    )
                    
                    # 2. 删除Qdrant中的重复问题
                    slave_ids = [slave['id'] for slave in slave_questions]
                    if slave_ids:
                        self.client.delete(
                            collection_name="qa_questions",
                            points_selector=slave_ids
                        )
                    
                    print(f"  ✅ Qdrant修复完成: 保留1个，删除{len(slave_questions)}个")
                    
                    # 3. 删除StarRocks中的重复记录
                    deleted_sr_count = self._delete_starrocks_duplicates(records_to_delete)
                    repair_stats['starrocks_records_deleted'] += deleted_sr_count
                    
                    print(f"  ✅ StarRocks修复完成: 删除{deleted_sr_count}条重复记录")
                    print(f"  ✅ 组修复完成: Qdrant(删除{len(slave_questions)}个) + StarRocks(删除{deleted_sr_count}条)")
                    
                except Exception as e:
                    print(f"  ❌ 组修复失败: {e}")
                    continue
            else:
                print(f"  📋 预览: Qdrant将保留1个，删除{len(slave_questions)}个")
                print(f"  📋 预览: StarRocks将删除{len(records_to_delete)}条记录")
            
            # 更新统计
            repair_stats['groups_processed'] += 1
            repair_stats['questions_merged'] += len(slave_questions)
            repair_stats['questions_deleted'] += len(slave_questions)
            repair_stats['questions_kept'] += 1
        
        action_text = "预览" if dry_run else "修复"
        print(f"\n[INFO] {action_text}完成:")
        print(f"  处理组数: {repair_stats['groups_processed']}")
        print(f"  Qdrant保留问题: {repair_stats['questions_kept']}")
        print(f"  Qdrant删除问题: {repair_stats['questions_deleted']}")
        print(f"  StarRocks删除记录: {repair_stats['starrocks_records_deleted']}")
        print(f"  合并问题: {repair_stats['questions_merged']}")
        
        return repair_stats
    
    def _select_master_question(self, group: List[Dict[str, Any]]) -> Dict[str, Any]:
        """选择主问题（保留哪一个）"""
        
        # 策略1: 选择相似问题列表最长的
        best_by_similar_count = max(group, key=lambda q: len(q['payload'].get('相似问题', [])))
        
        # 策略2: 如果有创建时间，选择最早的
        questions_with_time = [q for q in group if q['payload'].get('create_time')]
        if questions_with_time:
            earliest = min(questions_with_time, key=lambda q: q['payload']['create_time'])
            return earliest
            
        return best_by_similar_count
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息（包括StarRocks）"""
        try:
            # Qdrant统计
            info = self.client.get_collection("qa_questions")
            
            # 获取一些样本数据
            sample_result = self.client.scroll(
                collection_name="qa_questions",
                limit=10,
                with_payload=True
            )
            
            sample_points = sample_result[0] if sample_result else []
            
            # 统计需要修复的问题
            need_repair_count = 0
            for point in sample_points:
                if point.payload and point.payload.get('need_repair_check'):
                    need_repair_count += 1
            
            # StarRocks统计
            sr_stats = self._get_starrocks_stats()
            
            return {
                'qdrant_total_points': info.points_count,
                'qdrant_vector_size': info.config.params.vectors.size,
                'qdrant_distance_metric': info.config.params.vectors.distance.name,
                'qdrant_sample_need_repair': need_repair_count,
                'qdrant_sample_size': len(sample_points),
                'starrocks_total_records': sr_stats.get('total_records', 0),
                'starrocks_unique_standard_questions': sr_stats.get('unique_standard_questions', 0),
                'starrocks_unique_similar_questions': sr_stats.get('unique_similar_questions', 0)
            }
        except Exception as e:
            print(f"[ERROR] 获取集合统计信息失败: {e}")
            return {}

    def _get_starrocks_stats(self) -> Dict[str, Any]:
        """获取StarRocks数据库统计信息"""
        try:
            if not self.db._ensure_connection():
                return {}
                
            stats = {}
            
            # 总记录数
            self.db.cursor.execute("SELECT COUNT(*) FROM qa_pairs")
            stats['total_records'] = self.db.cursor.fetchone()[0]
            
            # 唯一标准问题数
            self.db.cursor.execute("SELECT COUNT(DISTINCT standard_question) FROM qa_pairs")
            stats['unique_standard_questions'] = self.db.cursor.fetchone()[0]
            
            # 唯一相似问题数
            self.db.cursor.execute("SELECT COUNT(DISTINCT similar_question) FROM qa_pairs")
            stats['unique_similar_questions'] = self.db.cursor.fetchone()[0]
            
            return stats
            
        except Exception as e:
            print(f"[ERROR] 获取StarRocks统计信息失败: {e}")
            return {}

    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.disconnect()
        print("[INFO] 数据库连接已关闭")

# 主修复脚本
async def main():
    parser = argparse.ArgumentParser(description="修复QA数据中的重复问题")
    parser.add_argument("--threshold", type=float, default=0.85, help="相似度阈值，默认0.85")
    parser.add_argument("--dry_run", action="store_true", help="只检查不修复（预览模式）")
    parser.add_argument("--batch_size", type=int, default=100, help="批量处理大小，默认100")
    parser.add_argument("--stats_only", action="store_true", help="只显示统计信息")
    args = parser.parse_args()
    
    repair_tool = QARepairTool(similarity_threshold=args.threshold, batch_size=args.batch_size)
    
    try:
        # 显示集合统计信息
        if args.stats_only:
            print("[INFO] 获取数据库统计信息...")
            stats = await repair_tool.get_collection_stats()
            if stats:
                print(f"数据库统计信息:")
                print(f"  Qdrant总问题数: {stats.get('qdrant_total_points', 'N/A')}")
                print(f"  Qdrant向量维度: {stats.get('qdrant_vector_size', 'N/A')}")
                print(f"  Qdrant距离度量: {stats.get('qdrant_distance_metric', 'N/A')}")
                print(f"  Qdrant样本中需修复: {stats.get('qdrant_sample_need_repair', 0)}/{stats.get('qdrant_sample_size', 0)}")
                print(f"  StarRocks总记录数: {stats.get('starrocks_total_records', 'N/A')}")
                print(f"  StarRocks唯一标准问题数: {stats.get('starrocks_unique_standard_questions', 'N/A')}")
                print(f"  StarRocks唯一相似问题数: {stats.get('starrocks_unique_similar_questions', 'N/A')}")
            return
        
        # 查找重复问题
        duplicate_groups = await repair_tool.find_duplicate_questions()
        
        if not duplicate_groups:
            print("[INFO] 🎉 未发现重复问题，数据质量良好！")
            return
        
        print(f"\n[INFO] 🔍 发现重复问题详情:")
        total_duplicates = 0
        for i, group in enumerate(duplicate_groups, 1):
            group_duplicates = len(group) - 1  # 除了保留的一个
            total_duplicates += group_duplicates
            print(f"  组 {i}: {len(group)} 个重复问题 (将删除 {group_duplicates} 个)")
            for j, q in enumerate(group):
                status = "🔸 保留" if j == 0 else "🔹 删除"
                print(f"    {status} \"{q['question'][:50]}...\" (ID: {q['id']})")
                
        print(f"\n[INFO] 📊 修复统计预览:")
        print(f"  重复组数: {len(duplicate_groups)}")
        print(f"  总重复问题: {sum(len(group) for group in duplicate_groups)}")
        print(f"  将删除: {total_duplicates} 个")
        print(f"  将保留: {len(duplicate_groups)} 个")
        
        if args.dry_run:
            print("\n[INFO] 🔍 这是预览模式，未执行实际修复")
            print("[INFO] 💡 要执行修复，请运行: python qa_repair_tool.py --threshold 0.85")
            return
        
        # 执行修复确认
        print(f"\n⚠️  即将删除 {total_duplicates} 个Qdrant重复问题及相关StarRocks记录，此操作不可逆！")
        confirmation = input("确认执行修复吗？(y/N): ")
        if confirmation.lower() == 'y':
            repair_stats = await repair_tool.repair_duplicate_groups(duplicate_groups, dry_run=False)
            print("\n[INFO] ✅ 修复完成！")
            print(f"[INFO] 修复统计:")
            print(f"  Qdrant删除问题: {repair_stats['questions_deleted']}")
            print(f"  StarRocks删除记录: {repair_stats['starrocks_records_deleted']}")
            print("[INFO] 💡 建议运行: python qa_repair_tool.py --stats_only 查看最新统计")
        else:
            print("[INFO] ❌ 取消修复")
    
    finally:
        # 确保关闭数据库连接
        repair_tool.close()

if __name__ == "__main__":
    asyncio.run(main()) 