#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import sys
import time
import hashlib
import config


def content_md5(content, ts, encrypt_key):
    """
    计算七鱼API认证校验和
    """
    nonce = hashlib.md5(content.encode("utf-8")).hexdigest()
    rlt = encrypt_key + nonce + ts
    sha1 = hashlib.sha1(rlt.encode("utf-8")).hexdigest()
    return sha1


def get_session_messages(session_id, mtypes=None, app_key=None, encrypt_key=None, timeout=30):
    """
    根据会话ID获取七鱼会话消息
    """
    url = "https://qiyukf.com/openapi/export/session/one/message"

    # 使用配置文件中的密钥（如果未提供）
    if app_key is None:
        app_key = config.QIYU_APPKEY
    if encrypt_key is None:
        encrypt_key = config.QIYU_ENCRYPT_KEY

    # 创建请求内容
    content_dict = {"sessionId": session_id}

    # 添加消息类型过滤（如果提供）
    if mtypes:
        content_dict["mTypes"] = mtypes

    # 转换为JSON字符串
    content = json.dumps(content_dict, separators=(",", ":"))

    # 获取时间戳
    ts = str(int(time.time()))

    # 计算校验和
    checksum = content_md5(content, ts, encrypt_key) if encrypt_key else None

    # 设置请求参数
    params = {}
    if app_key:
        params["appKey"] = app_key
    if checksum:
        params["time"] = ts
        params["checksum"] = checksum

    # 设置请求头
    headers = {
        "Content-Type": "application/json"
    }

    try:
        # 发送POST请求
        response = requests.post(url, data=content, headers=headers, params=params, timeout=timeout)
        response.raise_for_status()  # 如果响应状态码不是200，将引发HTTPError异常

        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}", file=sys.stderr)
        return {"code": 500, "message": f"请求错误: {str(e)}"}
    except Exception as e:
        print(f"未知错误: {e}", file=sys.stderr)
        return {"code": 500, "message": f"未知错误: {str(e)}"}


def format_session_messages(data):
    """
    格式化会话消息，并按时间排序
    """
    messages = data.get('data', [])  # 获取消息列表，默认为空列表

    # 检查消息是否包含时间字段并进行排序
    sorted_messages = sorted(messages, key=lambda x: x.get("time", 0))  # 按时间字段升序排序

    formatted_messages = []
    for message in sorted_messages:
        if isinstance(message, dict):
            session_id = message.get("sessionId", "未知")
            msg_id = message.get("id", "未知")
            msg_content = message.get("msg", "无消息内容")
            msg_time = time.strftime('%H:%M:%S', time.localtime(message.get("time", "未知时间") / 1000))
            from_type = message.get("from", -1)  # 获取消息流向
            staff_id = message.get("staffId", "未知")
            staff_name = message.get("staffName", "未知")
            user_id = message.get("userId", "未知")
            user_name = message.get("userName", "未知")
            auto_reply = message.get("autoReply", 0)

            # 根据消息流向确定发言人
            if from_type == 1:  # 来自访客
                speaker_id = user_id
                speaker_name = user_name
                speaker_role = "访客"
            elif from_type == 0:  # 来自客服
                speaker_id = staff_id
                speaker_name = staff_name
                speaker_role = "客服"
            else:
                speaker_id = "未知"
                speaker_name = "未知"
                speaker_role = "未知"

            # 格式化自动回复标记
            auto_reply_tag = "[自动回复]" if auto_reply == 1 else ""

            # 格式化输出
            formatted_messages.append(
                f"{msg_time}: "
                f" {speaker_role}{auto_reply_tag}: {speaker_name}:"
                f" {msg_content}"
            )
    return formatted_messages


if __name__ == '__main__':
    # 手动运行测试
    session_id = '11672824746'  # 替换为需要测试的会话ID
    result = get_session_messages(session_id)  # 获取会话消息
    print(result)  # 打印原始响应
    if result.get('code') == 200:  # 检查响应是否成功
        formatted_messages = format_session_messages(result)  # 格式化消息
        for formatted_message in formatted_messages:
            print(formatted_message)
    else:
        print(f"获取会话消息失败: {result.get('message', '未知错误')}", file=sys.stderr)
