#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用户诉求聚类分析系统
从 test.user_complaints 表查询用户诉求，使用 LanceDB 进行聚类分析
"""

import os
import json
import time
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import re
import argparse

# 机器学习库
try:
    from sklearn.cluster import KMeans, DBSCAN
    from sklearn.metrics import silhouette_score
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 scikit-learn: pip install scikit-learn")
    SKLEARN_AVAILABLE = False

# 语义模型
try:
    from openai import AsyncAzureOpenAI
    AZURE_OPENAI_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 openai: pip install openai")
    AZURE_OPENAI_AVAILABLE = False

# 保留原有的sentence-transformers作为备选
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("[WARN] sentence-transformers 不可用，仅使用Azure OpenAI")
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# 数据库
try:
    import lancedb
    LANCEDB_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 lancedb: pip install lancedb")
    LANCEDB_AVAILABLE = False

import config
from store_data_to_sr import StarRocksStore


class UserDemandClusteringAnalyzer:
    """用户诉求聚类分析器"""
    
    def __init__(self, use_azure_openai: bool = True, model_path: str = "./paraphrase-multilingual-MiniLM-L12-v2"):
        """初始化聚类分析器
        
        Args:
            use_azure_openai: 是否使用Azure OpenAI embedding服务
            model_path: 本地语义模型路径（备选方案）
        """
        print(f"[INFO] 初始化用户诉求聚类分析器...")
        
        self.use_azure_openai = use_azure_openai
        
        # 优先使用Azure OpenAI
        if use_azure_openai and AZURE_OPENAI_AVAILABLE:
            print(f"[INFO] 使用Azure OpenAI embedding服务")
            # 初始化 Azure OpenAI 客户端
            self.emb_client = AsyncAzureOpenAI(
                api_key="********************************",
                api_version="2024-05-01-preview",
                azure_endpoint="https://portai-eastus2.openai.azure.com/"
            )
            self.model = None
            print(f"[INFO] Azure OpenAI 客户端初始化成功，模型: text-embedding-3-small")
            
        elif SENTENCE_TRANSFORMERS_AVAILABLE:
            print(f"[INFO] 使用本地语义模型: {model_path}")
            try:
                self.model = SentenceTransformer(model_path)
                self.emb_client = None
                print(f"[INFO] 模型维度: {self.model.get_sentence_embedding_dimension()}")
            except Exception as e:
                print(f"[WARN] 无法加载本地模型，尝试使用在线模型: {e}")
                self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                self.emb_client = None
        else:
            raise ImportError("需要安装 openai 或 sentence-transformers")
        
        # 数据库连接
        self.sr_store = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE
        )
        
        # LanceDB
        if LANCEDB_AVAILABLE:
            self.db = lancedb.connect("./user_demand_clustering_db")
            print(f"[INFO] LanceDB 连接成功")
        else:
            self.db = None
            print(f"[WARN] LanceDB 不可用，结果将只保存为文件")
        
        # 统计信息
        self.results = {}
    
    def load_user_demands(self, limit: Optional[int] = None, 
                         date_filter: Optional[str] = None) -> pd.DataFrame:
        """从数据库加载用户诉求数据
        
        Args:
            limit: 限制查询数量
            date_filter: 日期过滤条件，格式如 "2024-01-01"
            
        Returns:
            用户诉求数据DataFrame
        """
        try:
            if not self.sr_store.connect():
                raise Exception("无法连接到数据库")
            
            # 构建查询语句
            query = """
            SELECT 
                user_demand,
                COUNT(*) as frequency,
                MIN(end_time) as first_occurrence,
                MAX(end_time) as last_occurrence
            FROM test.user_complaints
            WHERE user_demand IS NOT NULL 
            AND user_demand != ''
            AND LENGTH(TRIM(user_demand)) > 5
            """
            
            # 添加日期过滤
            if date_filter:
                query += f" AND DATE(end_time) >= '{date_filter}'"
            
            query += """
            GROUP BY user_demand
            ORDER BY frequency DESC
            """
            
            # 添加数量限制
            if limit:
                query += f" LIMIT {limit}"
            
            print(f"[INFO] 执行查询: {query}")
            self.sr_store.cursor.execute(query)
            results = self.sr_store.cursor.fetchall()
            
            columns = [desc[0] for desc in self.sr_store.cursor.description]
            df = pd.DataFrame(results, columns=columns)
            
            if df.empty:
                print(f"[WARN] 未查询到用户诉求数据")
                return df
            
            # 清理诉求文本
            df['demand_cleaned'] = df['user_demand'].apply(self._clean_text)
            df = df[df['demand_cleaned'].str.len() > 3].copy()
            
            print(f"[INFO] 成功加载 {len(df)} 个用户诉求")
            print(f"[INFO] 频次范围: {df['frequency'].min()} - {df['frequency'].max()}")
            print(f"[INFO] 时间范围: {df['first_occurrence'].min()} - {df['last_occurrence'].max()}")
            
            return df
            
        except Exception as e:
            print(f"[ERROR] 加载用户诉求数据失败: {e}")
            return pd.DataFrame()
        finally:
            self.sr_store.disconnect()
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        text = str(text).strip()
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符，保留中文、英文、数字、基本标点
        text = re.sub(r'[^\u4e00-\u9fff\w\s\?\!。？！，,、；;：:（）()]', '', text)
        return text.strip()
    
    async def _encode_texts_azure(self, texts: List[str]) -> List[List[float]]:
        """使用Azure OpenAI生成文本嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        embeddings = []
        batch_size = 100  # Azure OpenAI批量处理大小
        
        print(f"[INFO] 使用Azure OpenAI生成 {len(texts)} 个文本的嵌入向量...")
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            try:
                response = await self.emb_client.embeddings.create(
                    input=batch_texts,
                    model="text-embedding-3-small"
                )
                
                batch_embeddings = [data.embedding for data in response.data]
                embeddings.extend(batch_embeddings)
                
                print(f"[INFO] 已处理 {min(i + batch_size, len(texts))}/{len(texts)} 个文本")
                
            except Exception as e:
                print(f"[ERROR] Azure OpenAI embedding 生成失败: {e}")
                # 如果Azure失败，尝试使用本地模型作为备选
                if self.model is not None:
                    print(f"[INFO] 尝试使用本地模型作为备选...")
                    batch_embeddings = self.model.encode(batch_texts).tolist()
                    embeddings.extend(batch_embeddings)
                else:
                    raise e
        
        return embeddings
    
    def _encode_texts_local(self, texts: List[str]) -> List[List[float]]:
        """使用本地模型生成文本嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        print(f"[INFO] 使用本地模型生成 {len(texts)} 个文本的嵌入向量...")
        embeddings = self.model.encode(texts, show_progress_bar=True)
        return embeddings.tolist()
    
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """生成文本嵌入向量（统一接口）
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        if self.use_azure_openai and self.emb_client:
            return await self._encode_texts_azure(texts)
        elif self.model:
            return self._encode_texts_local(texts)
        else:
            raise RuntimeError("没有可用的embedding服务")
    
    async def perform_clustering(self, df: pd.DataFrame, 
                          method: str = "similarity_threshold",
                          similarity_threshold: float = 0.8,
                          n_clusters: int = 10) -> pd.DataFrame:
        """执行聚类分析
        
        Args:
            df: 用户诉求数据
            method: 聚类方法 ("similarity_threshold", "kmeans", "dbscan")
            similarity_threshold: 相似度阈值（用于similarity_threshold方法）
            n_clusters: 聚类数量（用于kmeans方法）
            
        Returns:
            带聚类标签的DataFrame
        """
        print(f"[INFO] 开始聚类分析，方法: {method}")
        
        # 生成嵌入向量
        demands = df['demand_cleaned'].tolist()
        embeddings = await self.encode_texts(demands)
        embeddings = np.array(embeddings)
        
        # 根据方法执行聚类
        if method == "similarity_threshold":
            cluster_labels = self._cluster_by_similarity(embeddings, similarity_threshold)
        elif method == "kmeans":
            cluster_labels = self._cluster_by_kmeans(embeddings, n_clusters)
        elif method == "dbscan":
            cluster_labels = self._cluster_by_dbscan(embeddings)
        else:
            raise ValueError(f"不支持的聚类方法: {method}")
        
        # 添加结果到DataFrame
        df = df.copy()
        df['cluster_id'] = cluster_labels
        df['embedding'] = embeddings.tolist()
        
        # 统计聚类结果
        unique_clusters = len(set(cluster_labels))
        print(f"[INFO] 聚类完成，共 {unique_clusters} 个聚类")
        
        # 聚类大小分布
        cluster_sizes = pd.Series(cluster_labels).value_counts().sort_index()
        print(f"[INFO] 聚类大小分布:")
        for cluster_id, size in cluster_sizes.head(10).items():
            print(f"  聚类 {cluster_id}: {size} 个诉求")
        
        return df
    
    def _cluster_by_similarity(self, embeddings: np.ndarray, 
                              threshold: float) -> List[int]:
        """基于相似度阈值的聚类"""
        print(f"[INFO] 使用相似度阈值聚类，阈值: {threshold}")
        
        similarity_matrix = cosine_similarity(embeddings)
        
        clusters = []
        used_indices = set()
        
        for i in range(len(embeddings)):
            if i in used_indices:
                continue
                
            # 找到所有与当前诉求相似的诉求
            similar_indices = []
            for j in range(len(embeddings)):
                if j not in used_indices and similarity_matrix[i][j] >= threshold:
                    similar_indices.append(j)
            
            if len(similar_indices) >= 1:  # 至少包含自己
                clusters.append(similar_indices)
                used_indices.update(similar_indices)
        
        # 分配聚类标签
        cluster_labels = [-1] * len(embeddings)
        for cluster_id, indices in enumerate(clusters):
            for idx in indices:
                cluster_labels[idx] = cluster_id
        
        return cluster_labels
    
    def _cluster_by_kmeans(self, embeddings: np.ndarray, 
                          n_clusters: int) -> List[int]:
        """K-means聚类"""
        print(f"[INFO] 使用K-means聚类，聚类数: {n_clusters}")
        
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(embeddings)
        
        # 计算轮廓系数
        if len(set(cluster_labels)) > 1:
            silhouette_avg = silhouette_score(embeddings, cluster_labels)
            print(f"[INFO] 轮廓系数: {silhouette_avg:.4f}")
        
        return cluster_labels.tolist()
    
    def _cluster_by_dbscan(self, embeddings: np.ndarray, 
                          eps: float = 0.3, min_samples: int = 2) -> List[int]:
        """DBSCAN聚类"""
        print(f"[INFO] 使用DBSCAN聚类，eps: {eps}, min_samples: {min_samples}")
        
        # 使用余弦距离
        from sklearn.metrics.pairwise import cosine_distances
        distance_matrix = cosine_distances(embeddings)
        
        dbscan = DBSCAN(eps=eps, min_samples=min_samples, metric='precomputed')
        cluster_labels = dbscan.fit_predict(distance_matrix)
        
        n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
        n_noise = list(cluster_labels).count(-1)
        
        print(f"[INFO] 发现 {n_clusters} 个聚类，{n_noise} 个噪声点")
        
        return cluster_labels.tolist()
    
    def analyze_clusters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析聚类结果
        
        Args:
            df: 带聚类标签的DataFrame
            
        Returns:
            聚类分析结果
        """
        print(f"[INFO] 分析聚类结果...")
        
        cluster_analysis = {}
        
        for cluster_id in sorted(df['cluster_id'].unique()):
            if cluster_id == -1:  # 跳过噪声点
                continue
                
            cluster_data = df[df['cluster_id'] == cluster_id].copy()
            
            if len(cluster_data) == 0:
                continue
            
            # 计算聚类内相似度
            embeddings = np.array([emb for emb in cluster_data['embedding']])
            if len(embeddings) > 1:
                similarity_matrix = cosine_similarity(embeddings)
                # 计算上三角矩阵的平均值（排除对角线）
                upper_triangle = similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)]
                avg_similarity = np.mean(upper_triangle) if len(upper_triangle) > 0 else 1.0
                min_similarity = np.min(upper_triangle) if len(upper_triangle) > 0 else 1.0
            else:
                avg_similarity = 1.0
                min_similarity = 1.0
            
            # 选择代表性诉求（最高频次或最接近中心的）
            if len(cluster_data) == 1:
                representative_demand = cluster_data.iloc[0]['demand_cleaned']
            else:
                # 选择频次最高的作为代表
                representative_demand = cluster_data.loc[cluster_data['frequency'].idxmax()]['demand_cleaned']
            
            # 收集聚类信息
            cluster_analysis[cluster_id] = {
                'representative_demand': representative_demand,
                'cluster_size': int(len(cluster_data)),
                'total_frequency': int(cluster_data['frequency'].sum()),
                'avg_frequency': float(cluster_data['frequency'].mean()),
                'avg_similarity': float(avg_similarity),
                'min_similarity': float(min_similarity),
                'all_demands': cluster_data['demand_cleaned'].tolist(),
                'frequency_distribution': [int(x) for x in cluster_data['frequency'].tolist()],
                'time_range': {
                    'first': str(cluster_data['first_occurrence'].min()),
                    'last': str(cluster_data['last_occurrence'].max())
                }
            }
        
        print(f"[INFO] 分析完成，共 {len(cluster_analysis)} 个有效聚类")
        
        # 不在这里进行类型转换，因为会影响LanceDB保存
        # cluster_analysis = self._convert_to_json_compatible(cluster_analysis)
        
        return cluster_analysis
    
    def save_to_lancedb(self, cluster_analysis: Dict[str, Any], 
                       table_name: str = "user_demand_clusters"):
        """保存聚类结果到LanceDB
        
        Args:
            cluster_analysis: 聚类分析结果
            table_name: 表名
        """
        if self.db is None:
            print("[WARN] LanceDB 不可用，跳过保存")
            return
        
        try:
            data = []
            for cluster_id, info in cluster_analysis.items():
                data.append({
                    'cluster_id': int(cluster_id),
                    'representative_demand': info['representative_demand'],
                    'cluster_size': info['cluster_size'],
                    'total_frequency': info['total_frequency'],
                    'avg_frequency': float(info['avg_frequency']),
                    'avg_similarity': info['avg_similarity'],
                    'min_similarity': info['min_similarity'],
                    'all_demands': info['all_demands'],
                    'frequency_distribution': info['frequency_distribution'],
                    'time_range': info['time_range'],
                    'created_at': datetime.now()
                })
            
            # 确保数据类型兼容
            # data = self._convert_to_json_compatible(data)
            
            # 删除旧表并创建新表
            if table_name in self.db.table_names():
                self.db.drop_table(table_name)
            
            table = self.db.create_table(table_name, data)
            print(f"[INFO] 聚类结果已保存到 LanceDB: {table_name}")
            
        except Exception as e:
            print(f"[ERROR] 保存到LanceDB失败: {e}")
    
    def _convert_to_json_compatible(self, obj):
        """递归转换所有numpy和pandas类型为JSON兼容的Python原生类型
        
        Args:
            obj: 需要转换的对象
            
        Returns:
            JSON兼容的对象
        """
        if isinstance(obj, dict):
            return {str(k): self._convert_to_json_compatible(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_compatible(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._convert_to_json_compatible(item) for item in obj)
        elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64, 
                             np.uint8, np.uint16, np.uint32, np.uint64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (pd.Timestamp, pd.Timedelta)):
            return str(obj)
        elif hasattr(obj, 'dtype') and 'int' in str(obj.dtype):
            # 处理pandas的整数类型
            return int(obj)
        elif hasattr(obj, 'dtype') and 'float' in str(obj.dtype):
            # 处理pandas的浮点类型
            return float(obj)
        elif hasattr(obj, 'item'):  # numpy scalar
            return obj.item()
        elif hasattr(obj, 'to_pydatetime'):  # pandas datetime
            return str(obj)
        elif str(type(obj)).startswith('<class \'pandas'):
            # 其他pandas类型，尝试转换为字符串
            return str(obj)
        elif str(type(obj)).startswith('<class \'numpy'):
            # 其他numpy类型，尝试转换
            try:
                return obj.item() if hasattr(obj, 'item') else str(obj)
            except:
                return str(obj)
        else:
            return obj
    
    def generate_report(self, df: pd.DataFrame, 
                       cluster_analysis: Dict[str, Any],
                       method: str, **kwargs) -> Dict[str, Any]:
        """生成聚类报告
        
        Args:
            df: 原始数据
            cluster_analysis: 聚类分析结果
            method: 聚类方法
            **kwargs: 其他参数
            
        Returns:
            报告数据
        """
        print(f"[INFO] 生成聚类报告...")
        
        # 基本统计
        total_demands = int(len(df))
        total_frequency = int(df['frequency'].sum())
        valid_clusters = int(len(cluster_analysis))
        noise_points = int(len(df[df['cluster_id'] == -1])) if -1 in df['cluster_id'].values else 0
        
        # 聚类质量评估
        if valid_clusters > 0:
            avg_cluster_size = float(np.mean([info['cluster_size'] for info in cluster_analysis.values()]))
            avg_similarity = float(np.mean([info['avg_similarity'] for info in cluster_analysis.values()]))
            cluster_sizes = [int(info['cluster_size']) for info in cluster_analysis.values()]
            frequency_coverage = float(sum([info['total_frequency'] for info in cluster_analysis.values()]) / total_frequency)
        else:
            avg_cluster_size = 0.0
            avg_similarity = 0.0
            cluster_sizes = []
            frequency_coverage = 0.0
        
        # 生成报告
        report = {
            'summary': {
                'total_demands': total_demands,
                'total_frequency': total_frequency,
                'valid_clusters': valid_clusters,
                'noise_points': noise_points,
                'method': method,
                'parameters': kwargs,
                'frequency_coverage': float(frequency_coverage)
            },
            'quality_metrics': {
                'avg_cluster_size': float(avg_cluster_size),
                'avg_similarity': float(avg_similarity),
                'cluster_size_distribution': {
                    'min': int(min(cluster_sizes)) if cluster_sizes else 0,
                    'max': int(max(cluster_sizes)) if cluster_sizes else 0,
                    'median': float(np.median(cluster_sizes)) if cluster_sizes else 0
                }
            },
            'cluster_details': cluster_analysis,
            'generated_at': datetime.now().isoformat()
        }
        
        # 转换为JSON兼容类型
        report = self._convert_to_json_compatible(report)
        
        return report
    
    async def run_analysis(self, limit: Optional[int] = None,
                    date_filter: Optional[str] = None,
                    method: str = "similarity_threshold",
                    **kwargs) -> Dict[str, Any]:
        """运行完整的聚类分析
        
        Args:
            limit: 限制查询数量
            date_filter: 日期过滤
            method: 聚类方法
            **kwargs: 聚类参数
            
        Returns:
            分析结果
        """
        print("=" * 80)
        print("用户诉求聚类分析系统")
        print("=" * 80)
        
        start_time = time.time()
        
        # 1. 加载数据
        print(f"\n[STEP 1] 加载用户诉求数据...")
        df = self.load_user_demands(limit=limit, date_filter=date_filter)
        if df.empty:
            print("[ERROR] 没有可分析的数据")
            return {}
        
        # 2. 执行聚类
        print(f"\n[STEP 2] 执行聚类分析...")
        clustered_df = await self.perform_clustering(df, method=method, **kwargs)
        
        # 3. 分析聚类结果
        print(f"\n[STEP 3] 分析聚类结果...")
        cluster_analysis = self.analyze_clusters(clustered_df)
        
        # 4. 保存到LanceDB
        print(f"\n[STEP 4] 保存结果...")
        self.save_to_lancedb(cluster_analysis)
        
        # 5. 生成报告
        print(f"\n[STEP 5] 生成报告...")
        report = self.generate_report(clustered_df, cluster_analysis, method, **kwargs)
        
        # 6. 输出结果
        duration = time.time() - start_time
        
        print(f"\n{'='*80}")
        print("聚类分析完成")
        print(f"{'='*80}")
        print(f"处理时间: {duration:.2f} 秒")
        print(f"输入诉求数: {report['summary']['total_demands']}")
        print(f"总频次: {report['summary']['total_frequency']}")
        print(f"聚类方法: {method}")
        print(f"Embedding服务: {'Azure OpenAI' if self.use_azure_openai else '本地模型'}")
        print(f"有效聚类数: {report['summary']['valid_clusters']}")
        print(f"噪声点数: {report['summary']['noise_points']}")
        print(f"频次覆盖率: {report['summary']['frequency_coverage']:.2%}")
        
        if report['summary']['valid_clusters'] > 0:
            print(f"\n聚类质量指标:")
            print(f"  平均聚类大小: {report['quality_metrics']['avg_cluster_size']:.1f}")
            print(f"  平均聚类内相似度: {report['quality_metrics']['avg_similarity']:.4f}")
            print(f"  聚类大小范围: {report['quality_metrics']['cluster_size_distribution']['min']} - {report['quality_metrics']['cluster_size_distribution']['max']}")
            
            print(f"\n前10个主要用户诉求类别:")
            print("-" * 80)
            
            # 按总频次排序显示
            sorted_clusters = sorted(
                cluster_analysis.items(),
                key=lambda x: x[1]['total_frequency'],
                reverse=True
            )
            
            for i, (cluster_id, info) in enumerate(sorted_clusters[:10], 1):
                print(f"{i:2d}. [{info['cluster_size']:2d}个诉求] {info['representative_demand']}")
                print(f"    总频次: {info['total_frequency']}, 平均频次: {info['avg_frequency']:.1f}")
                print(f"    相似度: {info['avg_similarity']:.3f}")
                print(f"    时间跨度: {info['time_range']['first']} ~ {info['time_range']['last']}")
                
                # 显示其他相似诉求（前3个）
                other_demands = [d for d in info['all_demands'] if d != info['representative_demand']][:3]
                if other_demands:
                    print(f"    相似诉求: {' | '.join(other_demands)}")
                print()
        
        # 保存详细结果到文件
        output_file = f'user_demand_clustering_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"[INFO] 详细报告已保存到: {output_file}")
        
        return report


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="用户诉求聚类分析")
    parser.add_argument("--limit", type=int, help="限制查询数量")
    parser.add_argument("--date_filter", type=str, help="日期过滤，格式: YYYY-MM-DD")
    parser.add_argument("--method", type=str, default="similarity_threshold",
                       choices=["similarity_threshold", "kmeans", "dbscan"],
                       help="聚类方法")
    parser.add_argument("--similarity_threshold", type=float, default=0.8,
                       help="相似度阈值 (用于similarity_threshold方法)")
    parser.add_argument("--n_clusters", type=int, default=10,
                       help="聚类数量 (用于kmeans方法)")
    parser.add_argument("--eps", type=float, default=0.3,
                       help="DBSCAN eps参数")
    parser.add_argument("--min_samples", type=int, default=2,
                       help="DBSCAN min_samples参数")
    parser.add_argument("--use_local_model", action="store_true",
                       help="使用本地模型而非Azure OpenAI")
    
    args = parser.parse_args()
    
    async def run_async_analysis():
        """异步运行分析"""
        try:
            analyzer = UserDemandClusteringAnalyzer(use_azure_openai=not args.use_local_model)
            
            # 准备聚类参数
            clustering_params = {}
            if args.method == "similarity_threshold":
                clustering_params['similarity_threshold'] = args.similarity_threshold
            elif args.method == "kmeans":
                clustering_params['n_clusters'] = args.n_clusters
            elif args.method == "dbscan":
                clustering_params['eps'] = args.eps
                clustering_params['min_samples'] = args.min_samples
            
            # 运行分析
            results = await analyzer.run_analysis(
                limit=args.limit,
                date_filter=args.date_filter,
                method=args.method,
                **clustering_params
            )
            
            if results:
                print(f"\n✅ 用户诉求聚类分析完成！")
                print(f"📊 发现 {results['summary']['valid_clusters']} 个主要诉求类别")
                print(f"📈 覆盖了 {results['summary']['frequency_coverage']:.1%} 的诉求频次")
                print(f"🚀 使用了 {'Azure OpenAI' if not args.use_local_model else '本地模型'} embedding服务")
            else:
                print(f"\n❌ 分析失败或无数据")
                
        except Exception as e:
            print(f"[ERROR] 分析失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行异步分析
    asyncio.run(run_async_analysis())


if __name__ == "__main__":
    main() 