import hashlib
import json
import time
import pyzipper
import requests
import datetime

class QiyuAPI:
    def __init__(self, appkey, encryptKey):
        self.appkey = appkey
        self.encryptKey = encryptKey
        self.headers = {
            "Content-Type": "application/json"
        }

    def _content_md5(self, content, ts):
        nonce = hashlib.md5(content.encode("utf-8")).hexdigest()
        print(f"Nonce: {nonce}")
        rlt = self.encryptKey + nonce + ts
        sha1 = hashlib.sha1(rlt.encode("utf-8")).hexdigest()
        return sha1

    def get_dt_content(self, dt: str):
        """获取指定日期的开始和结束时间戳（毫秒）"""
        try:
            year = int(dt[:4])
            month = int(dt[4:6])
            day = int(dt[6:])
            # 指定日期
            specified_date = datetime.datetime(year, month, day)

            # 北京时区
            beijing_timezone = datetime.timezone(datetime.timedelta(hours=8))

            # 指定日期北京时间的0点
            specified_date_beijing = datetime.datetime(specified_date.year, specified_date.month, specified_date.day,
                                                       tzinfo=beijing_timezone)
            specified_date_timestamp = str(int(specified_date_beijing.timestamp()) * 1000)

            # 前一天的日期
            previous_day = specified_date - datetime.timedelta(days=1)

            # 前一天的北京时间的0点
            previous_day_beijing = datetime.datetime(previous_day.year, previous_day.month, previous_day.day,
                                                     tzinfo=beijing_timezone)
            previous_day_timestamp = str(int(previous_day_beijing.timestamp()) * 1000)

            print(f"指定日期 {dt} 北京时间 0 点时间戳: {specified_date_timestamp}")
            print(f"前一天北京时间 0 点时间戳: {previous_day_timestamp}")

            return {"start": previous_day_timestamp, "end": specified_date_timestamp}
        except ValueError:
            print("日期格式错误，应为 YYYYMMDD")
            return None

    def get_qiyu_message(self, content):
        """请求七鱼导出 session 数据，返回 message ID。"""
        ts = str(int(time.time()))
        content_str = json.dumps(content, separators=(",", ":"))
        checksum = self._content_md5(content_str, ts)

        params = {
            "appKey": self.appkey,
            "time": ts,
            "checksum": checksum
        }
        url = "https://qiyukf.com/openapi/export/session"

        try:
            print(f"请求七鱼导出 Session, URL: {url}, Params: {params}, Data: {content_str}")
            response = requests.post(url, data=content_str, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()  # 检查请求是否成功
            result = response.json()
            print(f"七鱼导出 Session 响应: {result}")
            if result.get("code") == 200:
                return result.get("message")
            else:
                print(f"七鱼导出 Session API 错误: {result}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"请求七鱼导出 Session API 时发生错误: {e}")
            return None

    def get_qiyu_download_url(self, message_id):
        """根据 message ID 检查导出状态并获取下载 URL。"""
        ts = str(int(time.time()))
        content_dict = {"key": message_id}
        content = json.dumps(content_dict, separators=(",", ":"))
        checksum = self._content_md5(content, ts)

        params = {
            "appKey": self.appkey,
            "time": ts,
            "checksum": checksum
        }
        url = "https://qiyukf.com/openapi/export/session/check"

        try:
            while True:
                print(f"检查七鱼导出状态, URL: {url}, Params: {params}, Data: {content}")
                response = requests.post(url, data=content, headers=self.headers, params=params, timeout=30)
                response.raise_for_status()
                result = response.json()
                print(f"七鱼检查导出状态响应: {result}")
                code = result.get("code")
                if code == 200:
                    return result.get("message") # 返回下载 URL
                elif code == 14403: # 14403表示还需等待
                    print("导出任务仍在进行中，等待20秒后重试...")
                    time.sleep(20)
                else:
                    print(f"七鱼检查导出状态 API 错误: {result}")
                    return None
        except requests.exceptions.RequestException as e:
            print(f"请求七鱼检查导出状态 API 时发生错误: {e}")
            return None

    def download_file(self, url: str, file_name: str):
        """下载文件到指定路径。"""
        print(f"开始下载文件: {url} -> {file_name}")
        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"}
        try:
            response = requests.get(url, headers=headers, stream=True, timeout=60)
            response.raise_for_status()
            with open(file_name, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"文件下载成功: {file_name}")
            return True
        except requests.exceptions.RequestException as e:
            print(f"下载文件时发生错误: {e}")
            return False
        except Exception as e:
            print(f"写入文件时发生错误: {e}")
            return False

    def unzip_file(self, file_path: str, extract_to: str = "."):
        """解压加密的 zip 文件。"""
        password = self.appkey[:12] # 使用 appkey 的前12位作为密码
        print(f"开始解压文件: {file_path} 到 {extract_to}")
        try:
            with pyzipper.AESZipFile(file_path, 'r', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zf:
                zf.pwd = password.encode()
                zf.extractall(path=extract_to)
            print(f"文件解压成功: {extract_to}")
            return True
        except pyzipper.BadZipFile:
            print("解压失败：文件不是有效的 zip 文件或已损坏。")
            return False
        except RuntimeError as e:
             if 'password' in str(e).lower():
                 print("解压失败：密码错误。")
             else:
                 print(f"解压失败：运行时错误 {e}")
             return False
        except Exception as e:
            print(f"解压文件时发生未知错误: {e}")
            return False
