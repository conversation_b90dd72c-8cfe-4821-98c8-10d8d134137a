#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
读取 ./qa_results/ 目录下5月份的 qa_pairs 文件夹中的所有 JSON 数据
并将其存储到 StarRocks 数据库中
"""

import os
import json
import glob
import time
from datetime import datetime
from typing import Dict, Any, List

import config
from store_data_to_sr import StarRocksStore


class MayQAPairsImporter:
    """5月份QA对数据导入器"""
    
    def __init__(self, qa_results_dir: str = "./qa_results"):
        """初始化导入器
        
        Args:
            qa_results_dir: qa_results 目录路径
        """
        self.qa_results_dir = qa_results_dir
        
        # 初始化StarRocks存储器
        self.sr_store = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE,
            batch_size=100  # 批量处理100条记录
        )
        
        # 统计信息
        self.stats = {
            'total_directories': 0,
            'total_json_files': 0,
            'total_qa_pairs': 0,
            'successful_imports': 0,
            'failed_imports': 0,
            'processing_errors': 0
        }
    
    def get_may_directories(self) -> List[str]:
        """获取5月份的所有目录
        
        Returns:
            List[str]: 5月份目录列表
        """
        may_dirs = []
        
        # 查找所有以 2025-05- 开头的目录
        for item in os.listdir(self.qa_results_dir):
            full_path = os.path.join(self.qa_results_dir, item)
            if os.path.isdir(full_path) and item.startswith("2025-05-"):
                may_dirs.append(full_path)
        
        # 按目录名排序
        may_dirs.sort()
        
        print(f"[INFO] 找到 {len(may_dirs)} 个5月份的目录")
        for dir_path in may_dirs:
            print(f"  - {os.path.basename(dir_path)}")
            
        return may_dirs
    
    def get_qa_pairs_json_files(self, directory: str) -> List[str]:
        """获取指定目录下 qa_pairs 文件夹中的所有 JSON 文件
        
        Args:
            directory: 目录路径
            
        Returns:
            List[str]: JSON 文件路径列表
        """
        qa_pairs_dir = os.path.join(directory, "qa_pairs")
        
        if not os.path.exists(qa_pairs_dir):
            print(f"[WARN] qa_pairs 目录不存在: {qa_pairs_dir}")
            return []
        
        # 获取所有 JSON 文件
        json_files = glob.glob(os.path.join(qa_pairs_dir, "*.json"))
        
        print(f"[INFO] 在 {os.path.basename(directory)} 中找到 {len(json_files)} 个 JSON 文件")
        
        return json_files
    
    def load_json_file(self, file_path: str) -> Dict[str, Any]:
        """加载 JSON 文件
        
        Args:
            file_path: JSON 文件路径
            
        Returns:
            Dict[str, Any]: JSON 数据，如果加载失败则返回空字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data
        except Exception as e:
            print(f"[ERROR] 加载 JSON 文件失败 {file_path}: {e}")
            self.stats['processing_errors'] += 1
            return {}
    
    def convert_qa_json_to_starrocks_format(self, qa_json: Dict[str, Any]) -> Dict[str, Any]:
        """将 QA JSON 数据转换为 StarRocks 表格式
        
        Args:
            qa_json: QA JSON 数据
            
        Returns:
            Dict[str, Any]: StarRocks 表格式的数据
        """
        # 提取基本信息
        session_id = str(qa_json.get('session_id', ''))
        item_index = qa_json.get('item_index', 1)
        
        # 构建问题相关会话记录字符串
        question_relevant_list = qa_json.get('问题相关会话记录', [])
        question_relevant = '\n'.join(question_relevant_list) if question_relevant_list else ''
        
        # 构建答案相关会话记录字符串
        answer_relevant_list = qa_json.get('答案相关会话记录', [])
        answer_relevant = '\n'.join(answer_relevant_list) if answer_relevant_list else ''
        
        # 每个 QA 对的相似问题其实就是他的当下问题，而真正的标准问题是后续聚类找到最合适的问题，所以这里相似问题就是当下问题
        similar_question = qa_json.get('标准问题', '')
        
        # 构建 StarRocks 格式数据
        starrocks_data = {
            'session_id': session_id,
            'session_id_q': item_index,
            'catagory_1': qa_json.get('一级分类', ''),
            'catagory_2': qa_json.get('二级分类', ''),
            'catagory_3': qa_json.get('三级分类', ''),
            'standard_question': '',
            'similar_question': similar_question,
            'question_relevant': question_relevant,
            'standard_answer': qa_json.get('答案', ''),
            'answer_relevant': answer_relevant
        }
        
        return starrocks_data
    
    def process_json_file(self, file_path: str) -> bool:
        """处理单个 JSON 文件
        
        Args:
            file_path: JSON 文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 加载 JSON 数据
            qa_json = self.load_json_file(file_path)
            if not qa_json:
                return False
            
            # 转换为 StarRocks 格式
            starrocks_data = self.convert_qa_json_to_starrocks_format(qa_json)
            
            # 异步存储到数据库
            self.sr_store.store_qa_pair_async(starrocks_data)
            
            print(f"[DEBUG] 成功处理文件: {os.path.basename(file_path)}")
            print(f"  - 会话ID: {starrocks_data['session_id']}")
            print(f"  - 问题索引: {starrocks_data['session_id_q']}")
            print(f"  - 标准问题: {starrocks_data['standard_question']}")
            print(f"  - 分类: {starrocks_data['catagory_1']} > {starrocks_data['catagory_2']} > {starrocks_data['catagory_3']}")
            
            self.stats['successful_imports'] += 1
            return True
            
        except Exception as e:
            print(f"[ERROR] 处理文件失败 {file_path}: {e}")
            self.stats['failed_imports'] += 1
            return False
    
    def import_all_may_qa_pairs(self):
        """导入所有5月份的 QA 对数据"""
        print("=" * 60)
        print("开始导入5月份 QA 对数据到 StarRocks 数据库")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 获取5月份的所有目录
            may_directories = self.get_may_directories()
            self.stats['total_directories'] = len(may_directories)
            
            if not may_directories:
                print("[WARN] 未找到5月份的数据目录")
                return
            
            # 处理每个目录
            for directory in may_directories:
                print(f"\n[INFO] 处理目录: {os.path.basename(directory)}")
                
                # 获取 qa_pairs 中的 JSON 文件
                json_files = self.get_qa_pairs_json_files(directory)
                self.stats['total_json_files'] += len(json_files)
                
                # 处理每个 JSON 文件
                for json_file in json_files:
                    self.process_json_file(json_file)
                    self.stats['total_qa_pairs'] += 1
                    
                    # 每处理10个文件打印一次进度
                    if self.stats['total_qa_pairs'] % 10 == 0:
                        print(f"[PROGRESS] 已处理 {self.stats['total_qa_pairs']} 个 QA 对")
            
            # 等待所有异步任务完成
            print(f"\n[INFO] 所有文件处理完成，等待数据库写入完成...")
            
            # 等待一段时间让批量处理完成
            time.sleep(5)
            
        except Exception as e:
            print(f"[ERROR] 导入过程中发生错误: {e}")
            
        finally:
            # 断开数据库连接，这会触发剩余数据的刷新
            self.sr_store.disconnect()
            
            # 计算耗时
            end_time = time.time()
            duration = end_time - start_time
            
            # 打印统计信息
            self.print_summary(duration)
    
    def print_summary(self, duration: float):
        """打印导入摘要
        
        Args:
            duration: 处理耗时（秒）
        """
        print("\n" + "=" * 60)
        print("导入完成 - 统计摘要")
        print("=" * 60)
        print(f"处理耗时: {duration:.2f} 秒")
        print(f"5月份目录数: {self.stats['total_directories']}")
        print(f"JSON 文件总数: {self.stats['total_json_files']}")
        print(f"QA 对总数: {self.stats['total_qa_pairs']}")
        print(f"成功导入: {self.stats['successful_imports']}")
        print(f"导入失败: {self.stats['failed_imports']}")
        print(f"处理错误: {self.stats['processing_errors']}")
        
        if self.stats['total_qa_pairs'] > 0:
            success_rate = (self.stats['successful_imports'] / self.stats['total_qa_pairs']) * 100
            print(f"成功率: {success_rate:.2f}%")
        
        print("=" * 60)


def main():
    """主函数"""
    importer = MayQAPairsImporter()
    importer.import_all_may_qa_pairs()


if __name__ == "__main__":
    main() 