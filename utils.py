import datetime

def parse_time(iso_time_str):
    """将时间字符串转换为datetime对象，支持"YYYY-MM-DD HH:MM:SS"格式"""
    try:
        return datetime.datetime.strptime(iso_time_str, "%Y-%m-%d %H:%M:%S")
    except Exception as e:
        print(f"[WARN] 无法解析时间字符串 {iso_time_str}: {e}")
        return None

def format_timestamp(timestamp_ms):
    """将毫秒时间戳转换为 ISO 格式的时间字符串"""
    if not timestamp_ms:
        return ""
    try:
        dt = datetime.datetime.fromtimestamp(timestamp_ms / 1000)
        return dt.isoformat()
    except Exception as e:
        print(f"[WARN] 无法转换时间戳 {timestamp_ms}: {e}")
        return "" 