#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import time
import asyncio
import uuid
from typing import Dict, Any, List, Optional, Tuple, Union

import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import PointStruct, Filter, FieldCondition, MatchValue
from openai import AsyncAzureOpenAI

import config
from store_data_to_sr import StarRocksStore

class QASimilarityManager:
    """问答相似度管理器，用于管理相似问题的检测和存储"""

    def __init__(self, similarity_threshold: float = 0.75, batch_size: int = 100, use_combined_encoding: bool = False, fallback_threshold: float = 0.6):
        """初始化问答相似度管理器
        
        Args:
            similarity_threshold: 相似度阈值
            batch_size: StarRocks批量写入大小，默认100（用于测试）
            use_combined_encoding: 是否使用组合内容编码（问题+答案，问题权重更高）
            fallback_threshold: 兜底阈值，低于主阈值但高于此值的问题也会被归类
        """
        # 连接Qdrant服务
        self.client = QdrantClient(
            host="localhost", port=6333
            # url=config.QDRANT_URL,
            # api_key=config.QDRANT_API_KEY,
        )
        
        # 初始化 Azure OpenAI 客户端
        self.emb_client = AsyncAzureOpenAI(
            api_key="********************************",
            api_version="2024-05-01-preview",
            azure_endpoint="https://portai-eastus2.openai.azure.com/"
        )
        
        # 相似度阈值配置
        self.similarity_threshold = similarity_threshold
        self.fallback_threshold = fallback_threshold
        
        # 编码方式控制
        self.use_combined_encoding = use_combined_encoding
        
        # 添加Qdrant操作锁，确保查询-判断-写入操作的原子性
        self._qdrant_lock = asyncio.Lock()
        
        # 数据库连接，使用较小的批量大小进行测试
        self.db = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE,
            batch_size=batch_size  # 使用传入的批量大小
        )
        
        encoding_mode = "组合内容编码（问题主导）" if use_combined_encoding else "问题编码"
        print(f"[INFO] QASimilarityManager初始化完成，主阈值: {similarity_threshold}, 兜底阈值: {fallback_threshold}, 批量大小: {batch_size}, 编码模式: {encoding_mode}")
        
        # 确保集合存在
        self._ensure_collection_exists_sync()
        
    def _ensure_collection_exists_sync(self):
        """同步方式确保Qdrant集合存在，如果不存在则创建"""
        try:
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]
            print(f"当前存在的集合: {collection_names}")
            
            if "qa_questions" not in collection_names:
                print(f"创建Qdrant集合 'qa_questions'")
                
                # 创建集合，使用固定的向量维度（text-embedding-3-small 的维度是 1536）
                self.client.create_collection(
                    collection_name="qa_questions",
                    vectors_config=models.VectorParams(
                        size=1536,  # text-embedding-3-small 的固定维度
                        distance=models.Distance.COSINE
                    )
                )
                
                print(f"成功创建Qdrant集合 'qa_questions', 向量维度: 1536")
        except Exception as e:
            print(f"确保Qdrant集合存在时出错: {e}")
            raise  # 重新抛出异常，因为这是初始化步骤，失败应该终止程序
            
    async def initialize(self):
        """初始化管理器"""
        # 这里可以添加其他异步初始化操作
        pass
        
    def _generate_process_id(self, question_data: Dict[str, Any]) -> str:
        """为QA数据生成唯一的处理ID"""
        session_id = question_data.get('session_id', 'unknown')
        item_index = question_data.get('item_index', 0)
        return f"QA_{session_id}_{item_index}"
        
    def _log(self, process_id: str, message: str, level: str = "INFO"):
        """统一的日志输出格式"""
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        print(f"[{timestamp}] [{level}] [{process_id}] {message}")
        
    def _log_separator(self, process_id: str, title: str):
        """打印分隔符"""
        separator = "=" * 80
        print(f"\n{separator}")
        print(f"[{process_id}] {title}")
        print(separator)
        
    def _log_result(self, process_id: str, title: str, success: bool = True):
        """打印结果"""
        status = "SUCCESS" if success else "FAILED"
        print(f"\n[{process_id}] {title} - {status}")
        print("-" * 80)
        
    async def encode_text_content(self, text: str, process_id: str) -> List[float]:
        """将文本编码为向量（通用方法）
        
        Args:
            text: 文本内容
            process_id: 处理ID
            
        Returns:
            文本的向量表示
        """
        response = await self.emb_client.embeddings.create(
            input=text,
            model="text-embedding-3-small"
        )
        embedding = response.data[0].embedding
        return embedding
        
    async def encode_question_with_answer(self, question: str, answer: str = "", process_id: str = "") -> List[float]:
        """将问题和答案编码为向量，问题权重更高
        
        Args:
            question: 问题内容
            answer: 答案内容（可选）
            process_id: 处理ID
            
        Returns:
            组合文本的向量表示
        """
        # 构建加权文本：问题重复3次，答案1次，提高问题的权重
        text_parts = [question, question, question]  # 问题权重3
        if answer:
            text_parts.append(answer)  # 答案权重1
        
        combined_text = " ".join(text_parts)
        
        self._log(process_id, f"开始编码组合文本（问题主导）: 问题权重3, 答案权重1")
        self._log(process_id, f"组合文本长度: {len(combined_text)}字符")
        
        embedding = await self.encode_text_content(combined_text, process_id)
        
        self._log(process_id, f"编码完成，向量维度: {len(embedding)}")
        return embedding

    async def encode_question(self, question: str, process_id: str) -> List[float]:
        """将问题编码为向量（保持向后兼容）
        
        Args:
            question: 问题文本
            process_id: 处理ID
            
        Returns:
            问题的向量表示
        """
        self._log(process_id, f"开始编码问题: \"{question}\"")
        
        embedding = await self.encode_text_content(question, process_id)
        
        self._log(process_id, f"编码完成，向量维度: {len(embedding)}")
        return embedding
        
    async def find_similar_question(self, user_question: str, category_info: Dict[str, str] = None, process_id: str = None) -> Tuple[bool, Dict[str, Any]]:
        """用用户问题查找相似的历史标准问题
        
        Args:
            user_question: 用户问题
            category_info: 分类信息（当前版本已忽略）
            process_id: 处理ID
            
        Returns:
            (是否找到相似问题, 相似问题的信息)
        """
        self._log(process_id, f"开始查找相似的历史问题")
        self._log(process_id, f"用户问题: \"{user_question}\"")
        
        # 编码用户问题
        if self.use_combined_encoding:
            # 这里只有问题，没有答案，所以直接编码问题
            question_vector = await self.encode_question(user_question, process_id)
        else:
            question_vector = await self.encode_question(user_question, process_id)
        
        # 在Qdrant中搜索历史问题
        self._log(process_id, "在Qdrant中搜索相似的历史问题...")
        
        search_result = self.client.search(
            collection_name="qa_questions",
            query_vector=question_vector,
            limit=10
        )
        
        # 打印搜索结果
        self._log(process_id, f"搜索返回 {len(search_result)} 个结果")
        for i, point in enumerate(search_result[:5]):  # 显示前5个结果
            self._log(process_id, f"结果 {i+1}: ID={point.id}, 分数={point.score:.4f}, 标准问题=\"{point.payload.get('标准问题', '')}\"")
        
        # 多层次判断策略
        if search_result:
            best_match = search_result[0]
            score = best_match.score
            
            if score >= self.similarity_threshold:
                # 高相似度匹配
                self._log(process_id, f"找到高相似度匹配")
                self._log(process_id, f"ID: {best_match.id}")
                self._log(process_id, f"标准问题: \"{best_match.payload.get('标准问题')}\"")
                self._log(process_id, f"相似度: {score:.4f} (主阈值: {self.similarity_threshold})")
                
                return True, {
                    "point_id": best_match.id,
                    "payload": best_match.payload,
                    "score": score,
                    "match_type": "high_similarity"
                }
            elif score >= self.fallback_threshold:
                # 兜底匹配
                self._log(process_id, f"找到兜底匹配")
                self._log(process_id, f"ID: {best_match.id}")
                self._log(process_id, f"标准问题: \"{best_match.payload.get('标准问题')}\"")
                self._log(process_id, f"相似度: {score:.4f} (兜底阈值: {self.fallback_threshold})")
                
                return True, {
                    "point_id": best_match.id,
                    "payload": best_match.payload,
                    "score": score,
                    "match_type": "fallback"
                }
            else:
                # 相似度太低，不匹配
                self._log(process_id, f"未找到相似问题")
                self._log(process_id, f"最高相似度: {score:.4f} (兜底阈值: {self.fallback_threshold})")
                
                return False, {}
        else:
            self._log(process_id, f"搜索无结果")
            return False, {}
    
    ## 改为存储到 StarRocks 数据库中    
    def store_qa_to_sr(self, user_question_data: Dict[str, Any], is_similar: bool = False, standard_question_data: Dict[str, Any] = None, process_id: str = None):
        """存储QA对到StarRocks数据库
        
        Args:
            user_question_data: 用户问题数据
            is_similar: 是否找到相似问题
            standard_question_data: 标准问题数据（如果找到相似问题）
            process_id: 处理ID
        """
        try:
            self._log(process_id, f"准备存储QA对到StarRocks数据库")
            
            # 使用用户问题数据中的session_id和item_index
            session_id = str(user_question_data.get('session_id', ''))
            session_id_q = user_question_data.get('item_index', 0)
            
            # 处理问题相关会话记录（将数组转换为字符串）
            question_relevant_list = user_question_data.get('问题相关会话记录', [])
            question_relevant = '\n'.join(question_relevant_list) if isinstance(question_relevant_list, list) else str(question_relevant_list)
            
            # 处理答案相关会话记录（将数组转换为字符串）
            answer_relevant_list = user_question_data.get('答案相关会话记录', [])
            answer_relevant = '\n'.join(answer_relevant_list) if isinstance(answer_relevant_list, list) else str(answer_relevant_list)
            
            # 获取当前用户问题的答案（保持独立，不复用历史问题的答案）
            current_answer = user_question_data.get('推荐回复', user_question_data.get('答案', ''))
            
            # 准备存储数据
            if is_similar and standard_question_data:
                # 找到相似问题：标准问题来自历史数据，相似问题是当前用户问题
                # 但答案使用当前问题的独立答案，不复用历史答案
                store_data = {
                    'session_id': session_id,
                    'session_id_q': session_id_q,
                    'catagory_1': user_question_data.get('一级分类', ''),
                    'catagory_2': user_question_data.get('二级分类', ''),
                    'catagory_3': user_question_data.get('三级分类', ''),
                    'standard_question': standard_question_data.get('标准问题', ''),  # 历史的标准问题
                    'similar_question': user_question_data.get('标准问题', ''),      # 当前用户问题
                    'question_relevant': question_relevant,
                    'standard_answer': current_answer,  # 使用当前问题的独立答案，不复用历史答案
                    'answer_relevant': answer_relevant
                }
            else:
                # 未找到相似问题：当前问题成为新的标准问题
                store_data = {
                    'session_id': session_id,
                    'session_id_q': session_id_q,
                    'catagory_1': user_question_data.get('一级分类', ''),
                    'catagory_2': user_question_data.get('二级分类', ''),
                    'catagory_3': user_question_data.get('三级分类', ''),
                    'standard_question': user_question_data.get('标准问题', ''),     # 当前问题成为标准问题
                    'similar_question': user_question_data.get('标准问题', ''),     # 相似问题就是自己
                    'question_relevant': question_relevant,
                    'standard_answer': current_answer,  # 使用当前问题的独立答案
                    'answer_relevant': answer_relevant
                }
            
            self._log(process_id, f"会话: {store_data['session_id']} (问题序号: {store_data['session_id_q']})")
            self._log(process_id, f"分类: {store_data['catagory_1']} > {store_data['catagory_2']} > {store_data['catagory_3']}")
            self._log(process_id, f"标准问题: \"{store_data['standard_question']}\"")
            self._log(process_id, f"相似问题: \"{store_data['similar_question']}\"")
            self._log(process_id, f"独立答案: \"{store_data['standard_answer']}\"")
            self._log(process_id, f"问题会话记录: {len(question_relevant_list)}条")
            self._log(process_id, f"答案会话记录: {len(answer_relevant_list)}条")
            self._log(process_id, f"处理类型: {'归类到历史标准问题（保持独立答案）' if is_similar else '创建新标准问题'}")
            
            # 调用数据库存储方法
            success = self.db.store_qa_pair_async(store_data)
            
            self._log(process_id, f"QA对已添加到批量存储队列")
            return True
            
        except Exception as e:
            self._log(process_id, f"存储QA对到StarRocks数据库时发生错误: {e}", "ERROR")
            return False

    def _print_qa_info(self, user_question_data: Dict[str, Any], is_similar: bool = False, standard_question_data: Dict[str, Any] = None, process_id: str = None):
        """打印QA对的关键字段信息
        
        Args:
            user_question_data: 用户问题数据
            is_similar: 是否找到相似问题
            standard_question_data: 标准问题数据
            process_id: 处理ID
        """
        self._log(process_id, f"QA对信息总结:")
        self._log(process_id, f"一级分类: {user_question_data.get('一级分类', '')}")
        self._log(process_id, f"二级分类: {user_question_data.get('二级分类', '')}")
        self._log(process_id, f"三级分类: {user_question_data.get('三级分类', '')}")
        self._log(process_id, f"用户问题: {user_question_data.get('标准问题', '')}")
        
        if is_similar and standard_question_data:
            self._log(process_id, f"匹配到的标准问题: {standard_question_data.get('标准问题', '')}")
            self._log(process_id, f"处理结果: 归类到历史标准问题（保持独立答案）")
        else:
            self._log(process_id, f"处理结果: 创建新的标准问题")
        
        # 存储到StarRocks数据库
        self.store_qa_to_sr(user_question_data, is_similar, standard_question_data, process_id)

    async def add_question(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户问题（线程安全版本）
        
        Args:
            question_data: 用户问题数据，包含问题和其他元数据
            
        Returns:
            处理后的问题数据
        """
        # 生成处理ID
        process_id = self._generate_process_id(question_data)
        
        # 使用异步锁确保整个查询-判断-写入过程的原子性
        async with self._qdrant_lock:
            self._log_separator(process_id, f"开始处理用户问题")
            
            # 获取用户问题
            user_question = question_data.get("标准问题", "")
            if not user_question:
                self._log(process_id, "问题数据中必须包含'标准问题'字段", "ERROR")
                raise ValueError("问题数据中必须包含'标准问题'字段")
            
            self._log(process_id, f"用户问题: \"{user_question}\"")
            
            # 提取分类信息
            category_info = {
                "一级分类": question_data.get("一级分类", ""),
                "二级分类": question_data.get("二级分类", ""),
                "三级分类": question_data.get("三级分类", "")
            }
            
            self._log(process_id, f"分类信息: {category_info['一级分类']} > {category_info['二级分类']} > {category_info['三级分类']}")
                
            # 查找是否有相似的历史问题
            is_similar, similar_data = await self.find_similar_question(user_question, category_info, process_id)
            
            if is_similar:
                # 找到相似的历史问题
                point_id = similar_data["point_id"]
                standard_question_payload = similar_data["payload"]
                match_type = similar_data.get("match_type", "unknown")
                
                self._log(process_id, f"找到相似的历史问题（{match_type}）")
                
                # 更新历史问题的相似问题列表
                similar_questions = standard_question_payload.get("相似问题", [])
                if user_question not in similar_questions:
                    # 添加到相似问题列表
                    similar_questions.append(user_question)
                    
                    # 更新Qdrant中的payload
                    self._log(process_id, f"当前相似问题列表: {similar_questions}")
                    self.client.set_payload(
                        collection_name="qa_questions",
                        payload={"相似问题": similar_questions},
                        points=[point_id]
                    )
                    
                    self._log(process_id, f"已将用户问题添加到ID={point_id}的相似问题列表中")
                else:
                    self._log(process_id, f"用户问题已存在于相似问题列表中，无需重复添加")
                
                # 打印QA信息和存储
                self._print_qa_info(question_data, is_similar=True, standard_question_data=standard_question_payload, process_id=process_id)
                
                self._log_result(process_id, f"处理完成 - 归类到历史标准问题", True)
                
                # 返回标准问题的数据
                return standard_question_payload
            else:
                # 未找到相似问题，创建新的标准问题
                self._log(process_id, f"未找到相似问题，创建新的标准问题")
                
                # 准备payload
                user_answer = question_data.get("推荐回复", question_data.get("答案", ""))
                payload = {
                    "一级分类": question_data.get("一级分类", ""),
                    "二级分类": question_data.get("二级分类", ""),
                    "三级分类": question_data.get("三级分类", ""),
                    "标准问题": user_question,
                    "相似问题": [user_question],
                    "推荐回复": user_answer
                }
                
                # 根据配置选择编码方式
                if self.use_combined_encoding:
                    # 使用组合内容编码（问题主导）
                    self._log(process_id, f"使用组合内容编码模式（问题主导）")
                    question_vector = await self.encode_question_with_answer(user_question, user_answer, process_id)
                else:
                    # 使用纯问题编码
                    self._log(process_id, f"使用纯问题编码模式")
                    question_vector = await self.encode_question(user_question, process_id)
                
                # 生成唯一ID（使用微秒时间戳，提高唯一性）
                point_id = int(time.time() * 1000000)  
                self._log(process_id, f"生成Qdrant点ID: {point_id}")
                
                # 写入Qdrant
                self._log(process_id, f"写入Qdrant向量数据库...")
                self.client.upsert(
                    collection_name="qa_questions",
                    points=[
                        PointStruct(
                            id=point_id,
                            vector=question_vector,
                            payload=payload
                        )
                    ]
                )
                
                self._log(process_id, f"已创建新问题: ID={point_id}")
                
                # 打印QA信息和存储
                self._print_qa_info(question_data, process_id=process_id)
                
                self._log_result(process_id, f"处理完成 - 创建新标准问题", True)
                
                return payload

    def store_question_mapping(self, new_question: str, standard_question: str, point_id: str) -> bool:
        """存储问题映射关系到数据库
        
        Args:
            new_question: 新问题
            standard_question: 标准问题
            point_id: Qdrant中的点ID
            
        Returns:
            是否成功存储
        """
        print(f"存储问题映射关系: {new_question} -> {standard_question} (ID: {point_id})")
        return True
        # try:
        #     # 构建SQL插入语句
        #     query = """
        #     INSERT INTO qa_question_mappings (
        #         new_question, standard_question, point_id, create_time
        #     ) VALUES (%s, %s, %s, NOW())
        #     """
            
        #     # 确保数据库连接有效
        #     if not self.db._ensure_connection():
        #         print(f"存储问题映射时无法连接数据库")
        #         return False
                
        #     # 执行SQL
        #     self.db.cursor.execute(query, (new_question, standard_question, point_id))
        #     self.db.conn.commit()
            
        #     print(f"已将问题映射关系存入数据库: 新问题=\"{new_question}\" -> 标准问题=\"{standard_question}\"")
        #     return True
        # except Exception as e:
        #     print(f"存储问题映射关系到数据库失败: {e}")
        #     return False
            
    def get_question_by_id(self, point_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取问题
        
        Args:
            point_id: Qdrant中的点ID
            
        Returns:
            问题数据，如果不存在则返回None
        """
        try:
            print(f"\n根据ID获取问题: {point_id}")
            # 从Qdrant获取点
            points = self.client.retrieve(
                collection_name="qa_questions",
                ids=[point_id]
            )
            
            if points:
                print(f"找到问题: {json.dumps(points[0].payload, ensure_ascii=False, indent=2)}")
                return points[0].payload
            else:
                print(f"未找到ID为{point_id}的问题")
                return None
        except Exception as e:
            print(f"根据ID获取问题时出错: {e}")
            return None
            
    async def search_questions(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索问题
        
        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            
        Returns:
            问题列表
        """
        print(f"\n搜索问题: {query}")
        # 编码查询
        query_vector = await self.encode_question(query, "SEARCH")
        
        # 在Qdrant中搜索
        print("在Qdrant中搜索...")
        search_result = self.client.search(
            collection_name="qa_questions",
            query_vector=query_vector,
            limit=limit
        )
        
        # 格式化结果
        results = []
        for hit in search_result:
            results.append({
                "point_id": hit.id,
                "score": hit.score,
                "payload": hit.payload
            })
            
        print(f"搜索结果: {json.dumps(results, ensure_ascii=False, indent=2)}")
        return results
        
    def flush_batch_data(self):
        """手动刷新所有批量数据"""
        if self.db:
            print("💾 手动刷新批量数据...")
            self.db._flush_all_batches()
            
    def close(self):
        """关闭连接"""
        print("🔒 关闭连接并刷新所有批量数据...")
        if self.db:
            # 刷新所有批量数据
            self.db._flush_all_batches()
            # 断开数据库连接
            self.db.disconnect()

    async def add_question_concurrent(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """并发处理用户问题（无锁版本，允许重复）
        
        Args:
            question_data: 用户问题数据
            
        Returns:
            处理后的问题数据
        """
        process_id = self._generate_process_id(question_data)
        
        # 去掉异步锁，允许完全并发
        user_question = question_data.get("标准问题", "")
        if not user_question:
            self._log(process_id, "问题数据中必须包含'标准问题'字段", "ERROR")
            raise ValueError("问题数据中必须包含'标准问题'字段")
        
        self._log(process_id, f"并发模式处理问题: \"{user_question}\"")
        
        # 快速搜索相似问题（不加锁）
        is_similar, similar_data = await self.find_similar_question(user_question, None, process_id)
        
        # 添加时间戳和处理标识，便于后续修复
        current_time = time.time()
        
        if is_similar:
            # 找到相似问题，更新相似问题列表（可能有竞态，后续修复）
            point_id = similar_data["point_id"]
            standard_question_payload = similar_data["payload"]
            
            self._log(process_id, f"找到相似问题，快速归类")
            
            # 快速更新，不管竞态条件
            similar_questions = standard_question_payload.get("相似问题", [])
            if user_question not in similar_questions:
                similar_questions.append(user_question)
                try:
                    self.client.set_payload(
                        collection_name="qa_questions",
                        payload={"相似问题": similar_questions},
                        points=[point_id]
                    )
                except Exception as e:
                    self._log(process_id, f"更新相似问题列表失败，后续修复: {e}")
            
            # 存储QA对（添加修复标识）
            self._store_qa_pair_with_repair_info(question_data, True, standard_question_payload, process_id, current_time)
            return standard_question_payload
        else:
            # 创建新问题（可能重复，后续修复）
            self._log(process_id, f"未找到相似问题，创建新标准问题")
            
            user_answer = question_data.get("推荐回复", question_data.get("答案", ""))
            
            # 添加修复相关的metadata
            payload = {
                "一级分类": question_data.get("一级分类", ""),
                "二级分类": question_data.get("二级分类", ""),
                "三级分类": question_data.get("三级分类", ""),
                "标准问题": user_question,
                "相似问题": [user_question],
                "推荐回复": user_answer,
                # 修复相关字段
                "create_time": current_time,
                "need_repair_check": True,  # 标记需要修复检查
                "original_process_id": process_id
            }
            
            # 快速编码和写入
            if self.use_combined_encoding:
                question_vector = await self.encode_question_with_answer(user_question, user_answer, process_id)
            else:
                question_vector = await self.encode_question(user_question, process_id)
            
            point_id = int(time.time() * 1000000) + hash(process_id) % 1000  # 增加唯一性
            
            self.client.upsert(
                collection_name="qa_questions",
                points=[PointStruct(id=point_id, vector=question_vector, payload=payload)]
            )
            
            self._log(process_id, f"已创建新问题: ID={point_id}")
            
            # 存储QA对
            self._store_qa_pair_with_repair_info(question_data, False, None, process_id, current_time)
            
            return payload

    def _store_qa_pair_with_repair_info(self, user_question_data: Dict[str, Any], is_similar: bool, 
                                       standard_question_data: Dict[str, Any], process_id: str, create_time: float):
        """存储QA对时增加修复相关信息"""
        try:
            self._log(process_id, f"存储QA对到StarRocks数据库（并发模式）")
            
            session_id = str(user_question_data.get('session_id', ''))
            session_id_q = user_question_data.get('item_index', 0)
            
            question_relevant_list = user_question_data.get('问题相关会话记录', [])
            question_relevant = '\n'.join(question_relevant_list) if isinstance(question_relevant_list, list) else str(question_relevant_list)
            
            answer_relevant_list = user_question_data.get('答案相关会话记录', [])
            answer_relevant = '\n'.join(answer_relevant_list) if isinstance(answer_relevant_list, list) else str(answer_relevant_list)
            
            current_answer = user_question_data.get('推荐回复', user_question_data.get('答案', ''))
            
            if is_similar and standard_question_data:
                store_data = {
                    'session_id': session_id,
                    'session_id_q': session_id_q,
                    'catagory_1': user_question_data.get('一级分类', ''),
                    'catagory_2': user_question_data.get('二级分类', ''),
                    'catagory_3': user_question_data.get('三级分类', ''),
                    'standard_question': standard_question_data.get('标准问题', ''),
                    'similar_question': user_question_data.get('标准问题', ''),
                    'question_relevant': question_relevant,
                    'standard_answer': current_answer,
                    'answer_relevant': answer_relevant,
                    # 修复相关字段
                    'create_time_concurrent': create_time,
                    'need_repair_check': True,
                    'process_id': process_id
                }
            else:
                store_data = {
                    'session_id': session_id,
                    'session_id_q': session_id_q,
                    'catagory_1': user_question_data.get('一级分类', ''),
                    'catagory_2': user_question_data.get('二级分类', ''),
                    'catagory_3': user_question_data.get('三级分类', ''),
                    'standard_question': user_question_data.get('标准问题', ''),
                    'similar_question': user_question_data.get('标准问题', ''),
                    'question_relevant': question_relevant,
                    'standard_answer': current_answer,
                    'answer_relevant': answer_relevant,
                    # 修复相关字段
                    'create_time_concurrent': create_time,
                    'need_repair_check': True,
                    'process_id': process_id
                }
            
            self.db.store_qa_pair_async(store_data)
            self._log(process_id, f"QA对已添加到批量存储队列（并发模式）")
            
        except Exception as e:
            self._log(process_id, f"存储QA对失败: {e}", "ERROR")
            
    def enable_concurrent_mode(self):
        """启用并发模式（禁用锁）"""
        self._qdrant_lock = None
        print("[INFO] 已启用并发模式，禁用全局锁")

# 测试代码
if __name__ == "__main__":
    import asyncio
    
    async def main():
        # 初始化管理器
        manager = QASimilarityManager()  # 这里会同步创建集合
        await manager.initialize()  # 其他异步初始化操作
        
        # 测试添加问题
        test_question = {
            "一级分类": "产品",
            "二级分类": "交易",
            "三级分类": "订单",
            "标准问题": "如何查看我的订单历史？",
            "推荐回复": "您可以在APP首页点击'我的'->'订单记录'查看您的历史订单。"
        }
        
        result = await manager.add_question(test_question)
        print(f"添加结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 测试查找相似问题
        similar = await manager.search_questions("怎么查看订单历史记录")
        print(f"相似问题搜索结果: {json.dumps(similar, ensure_ascii=False, indent=2)}")
        
        # 关闭连接
        manager.close()
    
    asyncio.run(main()) 