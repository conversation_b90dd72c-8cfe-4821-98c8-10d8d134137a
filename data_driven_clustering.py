#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
纯数据驱动的二级用户诉求聚类分析
- 不使用预定义主题分类
- 基于embedding相似度自然形成层次结构
- 通过统计分析自动发现主题特征
"""

import os
import json
import time
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import argparse
from collections import Counter
import re

# 机器学习库
try:
    from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
    from sklearn.metrics import silhouette_score
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("[ERROR] 需要安装 scikit-learn")
    SKLEARN_AVAILABLE = False

# 导入现有的聚类分析器
from user_demand_clustering import UserDemandClusteringAnalyzer

class DataDrivenClusteringAnalyzer(UserDemandClusteringAnalyzer):
    """纯数据驱动的二级聚类分析器"""
    
    def __init__(self, use_azure_openai: bool = True, model_path: str = "./paraphrase-multilingual-MiniLM-L12-v2"):
        super().__init__(use_azure_openai, model_path)
    
    async def adaptive_hierarchical_clustering(self, df: pd.DataFrame, 
                                             level1_threshold: float = 0.85,
                                             level2_threshold: float = 0.7) -> Dict[str, Any]:
        """
        自适应二级层次聚类分析
        
        Args:
            df: 用户诉求数据
            level1_threshold: 第一级聚类相似度阈值（细粒度）
            level2_threshold: 第二级聚类相似度阈值（主题级）
            
        Returns:
            二级聚类结果
        """
        print(f"[INFO] 开始自适应二级层次聚类分析...")
        
        # 第一级：细粒度聚类（合并非常相似的诉求）
        print(f"[STEP 1] 第一级聚类 - 细粒度合并 (阈值: {level1_threshold})")
        level1_df = await self.perform_clustering(
            df, 
            method="similarity_threshold", 
            similarity_threshold=level1_threshold
        )
        
        level1_analysis = self.analyze_clusters(level1_df)
        print(f"[INFO] 第一级聚类完成，得到 {len(level1_analysis)} 个细分组")
        
        # 第二级：基于细分组的代表性诉求进行主题聚类
        print(f"[STEP 2] 第二级聚类 - 主题发现 (阈值: {level2_threshold})")
        
        # 准备第二级聚类数据
        level2_data = []
        for cluster_id, info in level1_analysis.items():
            level2_data.append({
                'level1_cluster_id': cluster_id,
                'representative_demand': info['representative_demand'],
                'cluster_size': info['cluster_size'],
                'total_frequency': info['total_frequency'],
                'avg_similarity': info['avg_similarity'],
                'all_demands': info['all_demands'],
                'time_range': info['time_range']
            })
        
        level2_df = pd.DataFrame(level2_data)
        level2_df['demand_cleaned'] = level2_df['representative_demand']
        
        # 对代表性诉求进行第二级聚类
        level2_clustered = await self.perform_clustering(
            level2_df,
            method="similarity_threshold", 
            similarity_threshold=level2_threshold
        )
        
        # 分析第二级聚类结果
        level2_analysis = self.analyze_level2_clusters(level2_clustered, level1_analysis)
        
        print(f"[INFO] 第二级聚类完成，发现 {len(level2_analysis)} 个主要主题")
        
        # 自动生成主题标签
        for topic_id, info in level2_analysis.items():
            info['auto_topic_label'] = self.generate_topic_label(info)
        
        return {
            'level1_analysis': level1_analysis,
            'level2_analysis': level2_analysis,
            'total_level1_clusters': len(level1_analysis),
            'total_level2_clusters': len(level2_analysis),
            'clustering_parameters': {
                'level1_threshold': level1_threshold,
                'level2_threshold': level2_threshold
            }
        }
    
    def analyze_level2_clusters(self, level2_df: pd.DataFrame, level1_analysis: Dict) -> Dict[str, Any]:
        """分析第二级聚类结果（纯数据驱动）"""
        level2_analysis = {}
        
        for cluster_id in sorted(level2_df['cluster_id'].unique()):
            if cluster_id == -1:
                continue
                
            cluster_data = level2_df[level2_df['cluster_id'] == cluster_id]
            
            # 收集这个主题下的所有子聚类信息
            subclusters = []
            total_frequency = 0
            total_demands = 0
            all_demands_list = []
            similarities = []
            time_ranges = []
            
            for _, row in cluster_data.iterrows():
                level1_id = row['level1_cluster_id']
                level1_info = level1_analysis[level1_id]
                
                subclusters.append({
                    'level1_cluster_id': level1_id,
                    'representative_demand': level1_info['representative_demand'],
                    'cluster_size': level1_info['cluster_size'],
                    'total_frequency': level1_info['total_frequency'],
                    'avg_similarity': level1_info['avg_similarity'],
                    'all_demands': level1_info['all_demands']
                })
                
                total_frequency += level1_info['total_frequency']
                total_demands += level1_info['cluster_size']
                all_demands_list.extend(level1_info['all_demands'])
                similarities.append(level1_info['avg_similarity'])
                time_ranges.append(level1_info['time_range'])
            
            # 选择最具代表性的诉求（按频次）
            main_subcluster = max(subclusters, key=lambda x: x['total_frequency'])
            representative_demand = main_subcluster['representative_demand']
            
            # 数据驱动的特征提取
            features = self.extract_data_driven_features(all_demands_list)
            
            # 计算时间范围
            all_first_times = [tr['first'] for tr in time_ranges]
            all_last_times = [tr['last'] for tr in time_ranges]
            
            level2_analysis[cluster_id] = {
                'representative_demand': representative_demand,
                'subclusters': subclusters,
                'subcluster_count': len(subclusters),
                'total_demands': total_demands,
                'total_frequency': total_frequency,
                'avg_frequency': float(total_frequency / total_demands) if total_demands > 0 else 0.0,
                'avg_similarity': float(np.mean(similarities)) if similarities else 0.0,
                'all_demands': list(set(all_demands_list)),  # 去重
                'features': features,
                'time_range': {
                    'first': min(all_first_times) if all_first_times else None,
                    'last': max(all_last_times) if all_last_times else None
                }
            }
        
        return level2_analysis
    
    def extract_data_driven_features(self, demands: List[str]) -> Dict[str, Any]:
        """数据驱动的特征提取"""
        # 合并所有诉求文本
        all_text = ' '.join(demands)
        
        # 1. 高频关键词提取（简单分词）
        # 移除标点和特殊字符，提取中文词汇
        words = re.findall(r'[\u4e00-\u9fff]+', all_text)
        word_freq = Counter(words)
        
        # 过滤掉过短的词和常见停用词
        stopwords = {'的', '了', '和', '与', '及', '或', '等', '在', '是', '有', '我', '你', '他', '她', '它'}
        filtered_words = {word: freq for word, freq in word_freq.items() 
                         if len(word) >= 2 and word not in stopwords and freq >= 2}
        
        top_keywords = sorted(filtered_words.items(), key=lambda x: x[1], reverse=True)[:8]
        
        # 2. 诉求长度统计
        demand_lengths = [len(demand) for demand in demands]
        
        # 3. 诉求复杂度（基于字符多样性）
        unique_chars = set(all_text)
        complexity_score = len(unique_chars) / len(all_text) if all_text else 0
        
        # 4. 模式识别（常见的诉求模式）
        patterns = self.identify_demand_patterns(demands)
        
        return {
            'top_keywords': [{'word': word, 'frequency': freq} for word, freq in top_keywords],
            'demand_statistics': {
                'count': len(demands),
                'avg_length': float(np.mean(demand_lengths)) if demand_lengths else 0.0,
                'length_std': float(np.std(demand_lengths)) if demand_lengths else 0.0,
                'min_length': int(min(demand_lengths)) if demand_lengths else 0,
                'max_length': int(max(demand_lengths)) if demand_lengths else 0
            },
            'complexity_score': float(complexity_score),
            'identified_patterns': patterns
        }
    
    def identify_demand_patterns(self, demands: List[str]) -> List[Dict[str, Any]]:
        """识别诉求中的常见模式"""
        patterns = []
        
        # 1. 查询类模式
        query_pattern = sum(1 for d in demands if any(word in d for word in ['查询', '查看', '确认', '了解']))
        if query_pattern > 0:
            patterns.append({
                'pattern_type': '查询类诉求',
                'count': query_pattern,
                'ratio': query_pattern / len(demands)
            })
        
        # 2. 问题类模式
        problem_pattern = sum(1 for d in demands if any(word in d for word in ['问题', '故障', '错误', '异常']))
        if problem_pattern > 0:
            patterns.append({
                'pattern_type': '问题反馈类',
                'count': problem_pattern,
                'ratio': problem_pattern / len(demands)
            })
        
        # 3. 申请类模式
        apply_pattern = sum(1 for d in demands if any(word in d for word in ['申请', '开通', '注册', '开户']))
        if apply_pattern > 0:
            patterns.append({
                'pattern_type': '申请办理类',
                'count': apply_pattern,
                'ratio': apply_pattern / len(demands)
            })
        
        # 4. 操作类模式
        action_pattern = sum(1 for d in demands if any(word in d for word in ['取消', '注销', '关闭', '删除']))
        if action_pattern > 0:
            patterns.append({
                'pattern_type': '操作执行类',
                'count': action_pattern,
                'ratio': action_pattern / len(demands)
            })
        
        return sorted(patterns, key=lambda x: x['count'], reverse=True)
    
    def generate_topic_label(self, topic_info: Dict[str, Any]) -> str:
        """基于数据自动生成主题标签"""
        features = topic_info['features']
        representative = topic_info['representative_demand']
        
        # 方法1：使用最高频的关键词
        if features['top_keywords']:
            top_keyword = features['top_keywords'][0]['word']
            label = f"{top_keyword}相关诉求"
        else:
            # 方法2：使用代表性诉求的关键部分
            if len(representative) > 8:
                label = representative[:8] + "类诉求"
            else:
                label = representative + "类诉求"
        
        # 方法3：结合模式信息
        if features['identified_patterns']:
            main_pattern = features['identified_patterns'][0]['pattern_type']
            if main_pattern != label:
                label = f"{label}({main_pattern})"
        
        return label
    
    def generate_data_driven_report(self, hierarchical_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成数据驱动的二级聚类报告"""
        print(f"[INFO] 生成数据驱动聚类报告...")
        
        level2_analysis = hierarchical_result['level2_analysis']
        
        # 按频次排序
        sorted_topics = sorted(
            level2_analysis.items(),
            key=lambda x: x[1]['total_frequency'],
            reverse=True
        )
        
        # 统计信息
        total_topics = len(level2_analysis)
        total_subclusters = sum(info['subcluster_count'] for info in level2_analysis.values())
        total_demands = sum(info['total_demands'] for info in level2_analysis.values())
        total_frequency = sum(info['total_frequency'] for info in level2_analysis.values())
        
        # 全局特征统计
        all_keywords = []
        all_patterns = []
        similarity_scores = []
        
        for info in level2_analysis.values():
            all_keywords.extend([kw['word'] for kw in info['features']['top_keywords']])
            all_patterns.extend([p['pattern_type'] for p in info['features']['identified_patterns']])
            similarity_scores.append(info['avg_similarity'])
        
        global_keyword_freq = Counter(all_keywords)
        global_pattern_freq = Counter(all_patterns)
        
        report = {
            'summary': {
                'total_topics': total_topics,
                'total_subclusters': total_subclusters,
                'total_demands': total_demands,
                'total_frequency': total_frequency,
                'analysis_method': 'data_driven_hierarchical_clustering',
                'clustering_parameters': hierarchical_result['clustering_parameters']
            },
            'global_insights': {
                'avg_topic_similarity': float(np.mean(similarity_scores)) if similarity_scores else 0.0,
                'top_global_keywords': [{'word': word, 'frequency': freq} 
                                      for word, freq in global_keyword_freq.most_common(10)],
                'pattern_distribution': [{'pattern': pattern, 'frequency': freq} 
                                       for pattern, freq in global_pattern_freq.most_common()],
                'topic_size_distribution': {
                    'min_subclusters': min([info['subcluster_count'] for info in level2_analysis.values()]) if level2_analysis else 0,
                    'max_subclusters': max([info['subcluster_count'] for info in level2_analysis.values()]) if level2_analysis else 0,
                    'avg_subclusters': float(np.mean([info['subcluster_count'] for info in level2_analysis.values()])) if level2_analysis else 0.0
                }
            },
            'detailed_topics': level2_analysis,
            'generated_at': datetime.now().isoformat()
        }
        
        # 转换为JSON兼容类型
        report = self._convert_to_json_compatible(report)
        
        return report, sorted_topics
    
    async def run_data_driven_analysis(self, limit: Optional[int] = None,
                                     date_filter: Optional[str] = None,
                                     level1_threshold: float = 0.85,
                                     level2_threshold: float = 0.7) -> Dict[str, Any]:
        """运行数据驱动的二级层次聚类分析"""
        print("=" * 80)
        print("数据驱动的二级用户诉求聚类分析系统")
        print("=" * 80)
        
        start_time = time.time()
        
        # 1. 加载数据
        print(f"\n[STEP 1] 加载用户诉求数据...")
        df = self.load_user_demands(limit=limit, date_filter=date_filter)
        if df.empty:
            print("[ERROR] 没有可分析的数据")
            return {}
        
        # 2. 执行自适应二级聚类
        print(f"\n[STEP 2] 执行自适应二级层次聚类...")
        hierarchical_result = await self.adaptive_hierarchical_clustering(
            df, 
            level1_threshold=level1_threshold,
            level2_threshold=level2_threshold
        )
        
        # 3. 生成数据驱动的报告
        print(f"\n[STEP 3] 生成数据驱动分析报告...")
        report, sorted_topics = self.generate_data_driven_report(hierarchical_result)
        
        # 4. 输出结果
        duration = time.time() - start_time
        
        print(f"\n{'='*80}")
        print("数据驱动聚类分析完成")
        print(f"{'='*80}")
        print(f"处理时间: {duration:.2f} 秒")
        print(f"输入诉求数: {report['summary']['total_demands']}")
        print(f"细分组数: {report['summary']['total_subclusters']}")
        print(f"主要主题数: {report['summary']['total_topics']}")
        print(f"总频次: {report['summary']['total_frequency']}")
        print(f"整体相似度: {report['global_insights']['avg_topic_similarity']:.3f}")
        
        # 显示全局洞察
        print(f"\n🔍 全局数据洞察:")
        print("-" * 50)
        print(f"高频关键词: {', '.join([kw['word'] for kw in report['global_insights']['top_global_keywords'][:5]])}")
        print(f"主要模式: {', '.join([p['pattern'] for p in report['global_insights']['pattern_distribution'][:3]])}")
        print(f"主题规模: 平均{report['global_insights']['topic_size_distribution']['avg_subclusters']:.1f}个子组")
        
        # 显示主要主题（数据驱动生成的标签）
        print(f"\n🎯 发现的主要诉求主题:")
        print("-" * 80)
        
        for i, (topic_id, info) in enumerate(sorted_topics[:15], 1):
            print(f"{i:2d}. 【{info['auto_topic_label']}】")
            print(f"    主要诉求: {info['representative_demand']}")
            print(f"    包含 {info['subcluster_count']} 个子组, {info['total_demands']} 个诉求")
            print(f"    总频次: {info['total_frequency']}, 平均相似度: {info['avg_similarity']:.3f}")
            
            # 显示数据特征
            keywords = [kw['word'] for kw in info['features']['top_keywords'][:3]]
            if keywords:
                print(f"    关键特征: {', '.join(keywords)}")
            
            patterns = [p['pattern_type'] for p in info['features']['identified_patterns'][:2]]
            if patterns:
                print(f"    诉求模式: {', '.join(patterns)}")
            
            # 显示重要子组
            if len(info['subclusters']) > 1:
                top_subclusters = sorted(info['subclusters'], key=lambda x: x['total_frequency'], reverse=True)[:2]
                print("    主要子组:")
                for sub in top_subclusters:
                    print(f"      - {sub['representative_demand']} (频次: {sub['total_frequency']})")
                if len(info['subclusters']) > 2:
                    print(f"      - ... 还有 {len(info['subclusters']) - 2} 个子组")
            print()
        
        # 保存详细结果到文件
        output_file = f'data_driven_clustering_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"[INFO] 详细报告已保存到: {output_file}")
        
        return report

    async def _encode_texts_azure(self, texts: List[str], batch_size: int = 100, max_concurrent: int = 5) -> List[List[float]]:
        """
        并发批量生成embedding
        Args:
            texts: 文本列表
            batch_size: 每批请求数量
            max_concurrent: 最大并发批次数
        Returns:
            嵌入向量列表
        """
        print(f"[INFO] 并发批量生成 {len(texts)} 个文本的嵌入向量...")
        batches = [texts[i:i + batch_size] for i in range(0, len(texts), batch_size)]
        semaphore = asyncio.Semaphore(max_concurrent)
        embeddings = [None] * len(texts)

        async def fetch_batch(batch_texts, batch_idx):
            async with semaphore:
                try:
                    response = await self.emb_client.embeddings.create(
                        input=batch_texts,
                        model="text-embedding-3-small"
                    )
                    return batch_idx, [data.embedding for data in response.data]
                except Exception as e:
                    print(f"[ERROR] embedding 生成失败: {e}")
                    return batch_idx, [None] * len(batch_texts)

        tasks = [fetch_batch(batch, idx) for idx, batch in enumerate(batches)]
        results = await asyncio.gather(*tasks)

        # 按顺序合并结果
        for idx, batch_embeddings in results:
            start = idx * batch_size
            embeddings[start:start+len(batch_embeddings)] = batch_embeddings

        return embeddings


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据驱动的二级用户诉求聚类分析")
    parser.add_argument("--limit", type=int, help="限制查询数量")
    parser.add_argument("--date_filter", type=str, help="日期过滤，格式: YYYY-MM-DD")
    parser.add_argument("--level1_threshold", type=float, default=0.85,
                       help="第一级聚类相似度阈值 (默认: 0.85)")
    parser.add_argument("--level2_threshold", type=float, default=0.7,
                       help="第二级聚类相似度阈值 (默认: 0.7)")
    parser.add_argument("--use_local_model", action="store_true",
                       help="使用本地模型而非Azure OpenAI")
    
    args = parser.parse_args()
    
    async def run_async_analysis():
        """异步运行分析"""
        try:
            analyzer = DataDrivenClusteringAnalyzer(use_azure_openai=not args.use_local_model)
            
            # 运行数据驱动的二级聚类分析
            results = await analyzer.run_data_driven_analysis(
                limit=args.limit,
                date_filter=args.date_filter,
                level1_threshold=args.level1_threshold,
                level2_threshold=args.level2_threshold
            )
            
            if results:
                print(f"\n✅ 数据驱动聚类分析完成！")
                print(f"📊 发现 {results['summary']['total_topics']} 个自然主题")
                print(f"🔍 包含 {results['summary']['total_subclusters']} 个细分组")
                print(f"📈 覆盖 {results['summary']['total_demands']} 个诉求")
                print(f"🚀 使用了 {'Azure OpenAI' if not args.use_local_model else '本地模型'} embedding服务")
            else:
                print(f"\n❌ 分析失败或无数据")
                
        except Exception as e:
            print(f"[ERROR] 分析失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行异步分析
    asyncio.run(run_async_analysis())


if __name__ == "__main__":
    # main() 
    # 1. 加载聚类报告
    with open('/Users/<USER>/Longbridge/vox-inspect/data_driven_clustering_report_20250613_115453.json', 'r', encoding='utf-8') as f:
        report = json.load(f)

    # 2. 获取所有主题
    topics = report['detailed_topics']

    # 3. 按总频次排序，取前100个
    sorted_topics = sorted(topics.items(), key=lambda x: x[1]['total_frequency'], reverse=True)[:100]

    # 4. 打印结果
    for i, (topic_id, info) in enumerate(sorted_topics, 1):
        print(f"{i:3d}. 【{info['auto_topic_label']}】")
        print(f"    主要诉求: {info['representative_demand']}")
        print(f"    包含 {info['subcluster_count']} 个子组, {info['total_demands']} 个诉求")
        print(f"    总频次: {info['total_frequency']}, 平均相似度: {info['avg_similarity']:.3f}")
        print()