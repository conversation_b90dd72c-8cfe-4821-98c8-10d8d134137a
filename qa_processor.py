#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import queue
import argparse
import threading
import datetime
from queue import Empty
from collections import defaultdict

import config
from qiyu_api import QiyuAPI
from prompt_service import PromptServiceAPI
from utils import parse_time, format_timestamp

class QAProcessor:
    """七鱼数据QA处理器，用于离线处理七鱼数据并发送到Prompt Service"""
    
    def __init__(self, output_dir=None, num_threads=8):
        """初始化QA处理器
        
        Args:
            output_dir: 输出目录，默认为'qa_results'
            num_threads: 处理线程数量
        """
        self.output_dir = output_dir or os.path.join(os.getcwd(), 'qa_results')
        self.num_threads = min(num_threads, 32)  # 最多32个线程
        self.qiyu_api = QiyuAPI(config.QIYU_APPKEY, config.QIYU_ENCRYPT_KEY)
        self.prompt_service = PromptServiceAPI(config.PROMPT_SERVICE_API_URL)
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 统计信息
        self.stats = defaultdict(int)
        self.lock = threading.Lock()
        
    def _convert_to_timestamp(self, time_input):
        """将不同格式的时间转换为毫秒时间戳
        
        Args:
            time_input: 时间输入，可以是以下格式之一：
                - 毫秒时间戳（整数）
                - 'YYYY-MM-DD' 格式的日期字符串
                - 'YYYY-MM-DD HH:mm:ss' 格式的日期时间字符串
                
        Returns:
            毫秒时间戳（整数）
        """
        if isinstance(time_input, (int, float)):
            return int(time_input)
        
        try:
            # 尝试解析完整的时间格式
            dt = datetime.datetime.strptime(time_input, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 尝试解析仅日期格式
                dt = datetime.datetime.strptime(time_input, "%Y-%m-%d")
            except ValueError:
                raise ValueError("时间格式必须是以下格式之一：毫秒时间戳、'YYYY-MM-DD' 或 'YYYY-MM-DD HH:mm:ss'")
        
        return int(dt.timestamp() * 1000)

    def process_data(self, start_date, end_date, test_mode=False):
        """处理指定日期范围的数据
        
        Args:
            start_date: 开始时间，可以是以下格式之一：
                - 毫秒时间戳（整数）
                - 'YYYY-MM-DD' 格式的日期字符串
                - 'YYYY-MM-DD HH:mm:ss' 格式的日期时间字符串
            end_date: 结束时间，格式要求同 start_date
            test_mode: 测试模式，只处理少量数据
            
        Returns:
            处理统计信息
        """
        # 转换为时间戳
        start_ts = self._convert_to_timestamp(start_date)
        end_ts = self._convert_to_timestamp(end_date)
        
        print(f"开始处理从 {start_date} 到 {end_date} 的七鱼数据")
        
        print(f"时间戳范围: {start_ts} -> {end_ts}")
        print(f"北京时间范围: {format_timestamp(start_ts)} -> {format_timestamp(end_ts)}")
        
        # 创建本次运行的输出目录
        run_id = f"{start_date}_to_{end_date}"
        run_output_dir = os.path.join(self.output_dir, run_id)
        os.makedirs(run_output_dir, exist_ok=True)
        
        # 从七鱼获取数据
        content = {"start": str(start_ts), "end": str(end_ts)}
        message_id = self.qiyu_api.get_qiyu_message(content)
        if not message_id:
            print("请求七鱼导出失败")
            return None
            
        print(f"成功请求七鱼导出，Message ID: {message_id}")
        
        # 轮询检查导出状态并获取下载链接
        download_url = self.qiyu_api.get_qiyu_download_url(message_id)
        if not download_url:
            print("获取下载链接失败")
            return None
            
        print(f"获取到下载链接: {download_url}")
        
        # 下载文件
        try:
            original_filename = download_url.split('/')[-1].split('?')[0]  # 尝试提取文件名
            if not original_filename.lower().endswith('.zip'):
                original_filename = f"qiyu_export_{run_id}.zip"
        except:
            original_filename = f"qiyu_export_{run_id}.zip"
            
        # 设置下载路径
        download_dir = os.path.join(run_output_dir, "downloads")
        extract_dir = os.path.join(run_output_dir, "extracted")
        os.makedirs(download_dir, exist_ok=True)
        os.makedirs(extract_dir, exist_ok=True)
        
        download_file_path = os.path.join(download_dir, original_filename)
        download_success = self.qiyu_api.download_file(download_url, download_file_path)
        if not download_success:
            print("下载文件失败")
            return None
            
        # 解压文件
        unzip_success = self.qiyu_api.unzip_file(download_file_path, extract_dir)
        if not unzip_success:
            print("解压文件失败")
            return None
            
        # 处理解压后的数据
        return self.process_extracted_data(extract_dir, run_id, test_mode)
        
    def process_extracted_data(self, extract_dir, run_id, test_mode=False):
        """处理解压后的数据
        
        Args:
            extract_dir: 解压目录
            run_id: 运行ID
            test_mode: 测试模式，只处理少量数据
            
        Returns:
            处理统计信息
        """
        print(f"开始处理解压目录: {extract_dir}")
        
        # 创建会话映射
        session_mapping = self._build_session_mapping(extract_dir)
        print(f"已解析 {len(session_mapping)} 个会话")
        
        if test_mode:
            # 测试模式下只处理10个会话
            print("测试模式: 只处理前10个会话")
            sessions_to_process = dict(list(session_mapping.items())[:10])
        else:
            sessions_to_process = session_mapping
            
        # 处理消息数据并构建对话
        conversations = self._build_conversations(extract_dir, sessions_to_process)
        print(f"已构建 {len(conversations)} 个对话")
        
        # 创建任务队列
        task_queue = queue.Queue()
        result_dict = {}
        result_lock = threading.Lock()
        
        # 将任务加入队列
        for session_id, conversation in conversations.items():
            formatted_data = self._format_conversation(conversation)
            if not formatted_data:
                continue
                
            prompt_payload = {
                "id": 176,
                "Q": formatted_data,
                "language": "zh-CN",
                "grpc_timeout": config.GRPC_TIME
            }
            
            task_queue.put((session_id, prompt_payload, conversation))
            self.stats['total_sessions'] += 1
            
        if task_queue.empty():
            print("没有可处理的会话")
            return self.stats
            
        # 启动工作线程
        threads = []
        for i in range(min(self.num_threads, task_queue.qsize())):
            thread = threading.Thread(
                target=self._worker_thread,
                args=(task_queue, result_dict, result_lock),
                name=f"Worker-{i+1}"
            )
            thread.daemon = True
            thread.start()
            threads.append(thread)
            
        # 等待所有任务处理完成
        task_queue.join()
        print("所有任务处理完成")
        
        # 处理和保存结果
        self._save_results(result_dict, run_id)
        
        return self.stats
        
    def _build_session_mapping(self, extract_dir):
        """构建会话ID到会话信息的映射
        
        Args:
            extract_dir: 解压目录
            
        Returns:
            会话映射字典 {session_id: {session_info}}
        """
        session_mapping = {}
        session_files = []
        
        # 寻找所有session.txt文件
        for root, dirs, files in os.walk(extract_dir):
            if 'session.txt' in files:
                session_files.append(os.path.join(root, 'session.txt'))
                
        for session_file in session_files:
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            if not line.strip().startswith('{'):
                                continue
                            session_data = json.loads(line.strip())
                            session_id = session_data.get("id")
                            if not session_id:
                                continue
                                
                            # 检查是否为机器人会话（staffId < 0）
                            staff_id = session_data.get("staffId")
                            if staff_id is not None and staff_id < 0:
                                print(f"跳过机器人会话 {session_id} (staffId={staff_id})")
                                continue
                                
                            # 提取会员ID
                            member_id = 0
                            user_crm_info = session_data.get("userCrmInfo")
                            if user_crm_info:
                                try:
                                    crm_data = json.loads(user_crm_info)
                                    for item in crm_data:
                                        if item.get("key") == "member_id":
                                            try:
                                                member_id = int(item.get("value", "0"))
                                            except (ValueError, TypeError):
                                                member_id = 0
                                            break
                                except json.JSONDecodeError:
                                    print(f"解析 userCrmInfo 失败: {user_crm_info}")
                                    
                            # 存储会话信息
                            session_mapping[session_id] = {
                                "member_id": member_id,
                                "staff_name": session_data.get("staffName"),
                                "user_name": session_data.get("userName"),
                                "staff_id": session_data.get("staffId"),
                                "user_id": session_data.get("userId"),
                                "create_time": session_data.get("createTime"),
                                "end_time": session_data.get("endTime")
                            }
                        except json.JSONDecodeError:
                            pass
                        except Exception as e:
                            print(f"处理会话行时出错: {e}")
            except Exception as e:
                print(f"读取会话文件 {session_file} 失败: {e}")
                
        return session_mapping
        
    def _build_conversations(self, extract_dir, session_mapping):
        """构建会话ID到对话内容的映射
        
        Args:
            extract_dir: 解压目录
            session_mapping: 会话映射
            
        Returns:
            对话映射字典 {session_id: {conversation_data}}
        """
        print(f"[DEBUG] 开始构建会话映射，会话映射表大小: {len(session_mapping)}")
        conversations = {}
        session_messages = self._build_session_messages(extract_dir)
        print(f"[DEBUG] 从消息文件中获取到 {len(session_messages)} 个会话的消息")
        
        for session_id, messages in session_messages.items():
            if session_id not in session_mapping:
                print(f"[DEBUG] 跳过未在会话映射表中的会话: {session_id}")
                continue
                
            # 按时间排序消息
            sorted_messages = sorted(messages, key=lambda x: x.get("time", 0))
            session_info = session_mapping[session_id]
            print(f"[DEBUG] 处理会话 {session_id}: 消息数量={len(sorted_messages)}, 客服={session_info['staff_name']}, 客户={session_info['user_name']}")
            
            conversation_data = {
                "session_id": session_id,
                "staff": {
                    "id": session_info["staff_id"],
                    "name": session_info["staff_name"]
                },
                "customer": {
                    "id": session_info["user_id"],
                    "name": session_info["user_name"],
                    "member_id": session_info["member_id"]
                },
                "create_time": session_info["create_time"],
                "end_time": session_info["end_time"],
                "messages": []
            }
            
            for msg in sorted_messages:
                # 跳过机器人消息
                staff_id = msg.get("staffId")
                if staff_id is not None and staff_id <= 0:
                    print(f"[DEBUG] 会话 {session_id} 跳过机器人消息: staffId={staff_id}")
                    continue
                    
                speaker = "客服" if msg.get("from") == 0 else "客户"
                speaker_name = session_info["staff_name"] if msg.get("from") == 0 else session_info["user_name"]
                time_stamp = msg.get("time", 0)
                
                # 处理消息内容
                msg_content = msg.get("msg", "")
                processed_msg = self._process_msg_content(msg_content, msg.get("mType"), msg)
                
                message_entry = {
                    "speaker": speaker,
                    "speaker_name": speaker_name,
                    "content": processed_msg,
                    "time": format_timestamp(time_stamp) if time_stamp else "",
                    "msg_id": msg.get("id")
                }
                conversation_data["messages"].append(message_entry)
                
            if conversation_data["messages"]:
                print(f"[DEBUG] 会话 {session_id} 构建完成: 有效消息数={len(conversation_data['messages'])}")
                conversations[session_id] = conversation_data
            else:
                print(f"[DEBUG] 会话 {session_id} 没有有效消息，跳过")
                
        print(f"[DEBUG] 会话构建完成，共构建 {len(conversations)} 个有效会话")
        return conversations
    
    def _process_msg_content(self, msg_content, msg_type, message_data=None):
        """处理消息内容，解析JSON和富文本
        
        Args:
            msg_content: 原始消息内容
            msg_type: 消息类型
            message_data: 完整消息数据
            
        Returns:
            处理后的消息内容
        """
        if not msg_content:
            return ""
            
        # 特定消息类型描述
        MESSAGE_TYPE_DESC = {
            0: "系统消息",
            1: "文本消息",
            2: "图片消息",
            3: "语音消息",
            4: "文件消息",
            5: "视频消息",
            6: "系统提示消息",
            100: "自定义消息",
            110: "机器人答案",
            111: "机器人答案反馈",
            115: "富文本消息",
            123: "emoji消息"
        }
        
        # 获取消息类型描述
        def get_message_type_desc(mType):
            return MESSAGE_TYPE_DESC.get(mType, f"未知消息类型({mType})")
            
        # 提取前缀和后缀
        prefixes = []
        suffixes = []
        
        if message_data:
            # 检查是否为自动回复
            auto_reply = message_data.get("autoReply")
            if auto_reply == 1:
                prefixes.append("[自动回复]")
                
            # 检查消息状态
            status = message_data.get("status")
            if status == 2:
                prefixes.append("[已撤回]")
                
        # 处理不同类型的消息
        if msg_type in [2, 3, 4, 5]:
            # 媒体类型消息，保留原始内容和类型描述
            type_desc = f"[{get_message_type_desc(msg_type)}]"
            if msg_content and msg_content.strip():
                # 如果有原始内容，同时显示类型和内容
                return f"{' '.join(prefixes)} {type_desc} {msg_content} {' '.join(suffixes)}".strip()
            else:
                # 如果没有原始内容，只显示类型
                return f"{' '.join(prefixes)} {type_desc} {' '.join(suffixes)}".strip()
            
        # 处理可能是JSON的消息
        if isinstance(msg_content, str) and (msg_content.startswith("{") or msg_content.startswith("[")):
            try:
                msg_json = json.loads(msg_content)
                
                # 富文本消息
                if msg_type == 115 and isinstance(msg_json, dict) and "content" in msg_json:
                    import html
                    import re
                    content = msg_json.get("content", "")
                    clean_text = html.unescape(re.sub(r'<[^>]+>', ' ', content)).strip()
                    return f"{' '.join(prefixes)} {clean_text}".strip()
                    
                # 机器人答案
                elif msg_type == 110 and isinstance(msg_json, dict):
                    if "content" in msg_json:
                        return f"{' '.join(prefixes)} {msg_json.get('content', '')}".strip()
                    else:
                        return f"{' '.join(prefixes)} {json.dumps(msg_json, ensure_ascii=False)}".strip()
                        
                # emoji消息
                elif msg_type == 123 and isinstance(msg_json, dict):
                    if "content" in msg_json:
                        return f"{' '.join(prefixes)} [表情] {msg_json.get('content', '')}".strip()
                    else:
                        return f"{' '.join(prefixes)} [表情]".strip()
                        
                # 其他JSON结构
                return f"{' '.join(prefixes)} {json.dumps(msg_json, ensure_ascii=False)}".strip()
                
            except json.JSONDecodeError:
                # 解析失败，返回原始内容
                pass
                
        # 普通文本消息
        return f"{' '.join(prefixes)} {msg_content}".strip()
        
    def _format_conversation(self, conversation_data):
        """将对话数据格式化为字符串
        
        Args:
            conversation_data: 对话数据
            
        Returns:
            格式化后的对话字符串
        """
        if not conversation_data or not conversation_data.get("messages"):
            print("[DEBUG] 对话数据为空或没有消息")
            return ""
            
        session_id = conversation_data.get("session_id", "未知")
        print(f"[DEBUG] 开始格式化会话 {session_id} 的对话内容")
        
        staff_info = conversation_data.get("staff", {})
        customer_info = conversation_data.get("customer", {})
        messages = conversation_data.get("messages", [])
        
        print(f"[DEBUG] 会话 {session_id} 信息: 客服={staff_info.get('name')}, 客户={customer_info.get('name')}, 消息数={len(messages)}")
        
        # 构建格式化字符串
        result = []
        result.append(f"会话: {session_id} 详情:")
        # result.append(f"客服信息: ID={staff_info.get('id', '未知')}, 姓名={staff_info.get('name', '未知')}")
        # result.append(f"客户信息: ID={customer_info.get('id', '未知')}, 姓名={customer_info.get('name', '未知')}, 会员ID={customer_info.get('member_id', '未知')}")
        
        # # 添加会话时间信息
        # create_time = conversation_data.get("create_time")
        # end_time = conversation_data.get("end_time")
        # if create_time:
        #     # 检查时间戳类型
        #     if isinstance(create_time, (int, float)):
        #         result.append(f"会话开始时间: {format_timestamp(create_time)}")
        #     else:
        #         # 已经是格式化的时间字符串
        #         result.append(f"会话开始时间: {create_time}")
        #
        # if end_time:
        #     # 检查时间戳类型
        #     if isinstance(end_time, (int, float)):
        #         result.append(f"会话结束时间: {format_timestamp(end_time)}")
        #     else:
        #         # 已经是格式化的时间字符串
        #         result.append(f"会话结束时间: {end_time}")
        #
        # result.append("\n对话内容:")
        
        valid_messages = 0
        for i, msg in enumerate(messages, 1):
            speaker = msg.get("speaker", "未知")
            speaker_name = msg.get("speaker_name", "未知")
            content = msg.get("content", "")
            time_str = msg.get("time", "")
            
            # 跳过空内容
            if not content:
                print(f"[DEBUG] 会话 {session_id} 跳过第 {i} 条空消息")
                continue
                
            result.append(f"{i}. {speaker}({speaker_name}) [{time_str}]: {content}")
            valid_messages += 1
            
        formatted_result = "\n".join(result)
        print(f"[DEBUG] 会话 {session_id} 格式化完成: 总消息数={len(messages)}, 有效消息数={valid_messages}")
        print(f"[DEBUG] 格式化后的内容: {formatted_result} ")
        
        return formatted_result
        
    def _worker_thread(self, task_queue, result_dict, result_lock):
        """工作线程，处理任务队列中的任务
        
        Args:
            task_queue: 任务队列
            result_dict: 结果字典
            result_lock: 结果字典的锁
        """
        while True:
            try:
                # 从队列获取任务
                session_id, prompt_payload, conversation = task_queue.get(timeout=1)
            except Empty:
                break
                
            start_time = time.time()
            print(f"[线程 {threading.current_thread().name}] 处理会话: {session_id}")
            
            try:
                # 发送请求到Prompt Service
                result = self.prompt_service.send_prompt(prompt_payload)
                end_time = time.time()
                duration = end_time - start_time
                
                with self.lock:
                    if result:
                        # 提取核心的AI响应结果字符串
                        ai_response = result.get('ai_response')
                        if not ai_response or not isinstance(ai_response, dict):
                            self.stats['fail_count'] += 1
                            print(f"[会话 {session_id}] 响应中缺少 'ai_response' 或格式不正确，请求失败，耗时: {duration:.2f}秒")
                            print(f"原始响应: {result}")
                            continue
                            
                        result_str = ai_response.get('result')
                        if not result_str or not isinstance(result_str, str):
                            self.stats['fail_count'] += 1
                            print(f"[会话 {session_id}] 'ai_response' 中缺少 'result' 字符串或格式不正确，请求失败，耗时: {duration:.2f}秒")
                            print(f"ai_response: {ai_response}")
                            continue
                            
                        try:
                            # 清理可能的markdown代码块标记
                            cleaned_result_str = result_str.strip().strip('```json').strip('```').strip()
                            parsed_data = json.loads(cleaned_result_str)
                            
                            self.stats['success_count'] += 1
                            print(f"[会话 {session_id}] 请求成功，耗时: {duration:.2f}秒")
                            
                            # 处理和保存结果
                            with result_lock:
                                result_dict[session_id] = {
                                    'conversation': conversation,
                                    'prompt_payload': prompt_payload,
                                    'result': parsed_data,  # 使用解析后的JSON对象
                                    'duration': duration
                                }
                        except json.JSONDecodeError as e:
                            self.stats['fail_count'] += 1
                            print(f"[会话 {session_id}] 解析JSON失败: {e}，耗时: {duration:.2f}秒")
                            print(f"结果字符串(前500字符): {result_str[:500]}...")
                    else:
                        self.stats['fail_count'] += 1
                        print(f"[会话 {session_id}] 请求失败，耗时: {duration:.2f}秒")
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"[会话 {session_id}] 处理出错: {e}，耗时: {duration:.2f}秒")
                with self.lock:
                    self.stats['error_count'] += 1
            finally:
                task_queue.task_done()
                
    def _save_results(self, result_dict, run_id):
        """保存处理结果
        
        Args:
            result_dict: 结果字典
            run_id: 运行ID
        """
        qa_dir = os.path.join(self.output_dir, run_id, 'qa_pairs')
        os.makedirs(qa_dir, exist_ok=True)
        
        total_qa_pairs = 0
        
        for session_id, data in result_dict.items():
            try:
                # 获取会话信息和结果
                conversation = data['conversation']
                result = data['result']  # 已经是解析后的JSON对象
                staff_name = conversation['staff']['name']
                customer_name = conversation['customer']['name']
                member_id = conversation['customer']['member_id']
                
                # 解析QA对的Prompt Service返回格式
                if 'data' in result and isinstance(result['data'], list):
                    qa_items = result['data']
                    print(f"[会话 {session_id}] 发现 {len(qa_items)} 个QA对")
                    
                    # 如果返回了多个QA对，处理每一个
                    for idx, qa_item in enumerate(qa_items):
                        try:
                            # 构造QA对
                            qa_pair = {
                                'session_id': session_id,
                                'item_index': idx,
                                'staff_name': staff_name,
                                'customer_name': customer_name,
                                'member_id': member_id,
                                '标准问题': qa_item.get('标准问题', ''),
                                '问题相关会话记录': qa_item.get('问题相关会话记录', []),
                                '答案': qa_item.get('答案', ''),
                                '答案相关会话记录': qa_item.get('答案相关会话记录', []),
                                '一级分类': qa_item.get('一级分类', ''),
                                '二级分类': qa_item.get('二级分类', ''),
                                '三级分类': qa_item.get('三级分类', ''),
                                'original_conversation': data['prompt_payload']['Q'],
                                'processed_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                            
                            # 保存为JSON文件，对于多个QA对，使用索引区分
                            output_file = os.path.join(qa_dir, f"{session_id}_{idx}.json")
                            with open(output_file, 'w', encoding='utf-8') as f:
                                json.dump(qa_pair, f, ensure_ascii=False, indent=2)
                                
                            total_qa_pairs += 1
                            
                            print(f"[会话 {session_id}] 已保存QA对 {idx + 1}")
                            
                        except Exception as item_e:
                            print(f"处理会话 {session_id} 的QA对 {idx} 时出错: {item_e}")
                else:
                    # 处理不符合预期格式的情况
                    print(f"会话 {session_id} 的返回结果格式不符合预期或没有QA对: {result}")
                    
                    # 保存原始返回数据以便后续分析
                    error_file = os.path.join(qa_dir, f"{session_id}_error.json")
                    with open(error_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            'session_id': session_id,
                            'prompt': data['prompt_payload']['Q'],
                            'result': result,
                            'error': '返回格式不符合预期'
                        }, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                print(f"保存会话 {session_id} 结果时出错: {e}")
                
        # 更新统计信息
        self.stats['total_qa_pairs'] = total_qa_pairs
                
        # 保存汇总文件
        summary = {
            'run_id': run_id,
            'process_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'stats': dict(self.stats),
            'total_qa_pairs': total_qa_pairs,
            'sessions': list(result_dict.keys())
        }
        
        summary_file = os.path.join(self.output_dir, run_id, 'summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
            
        print(f"处理结果已保存到 {qa_dir}")
        print(f"共生成 {total_qa_pairs} 个QA对")
        print(f"汇总信息已保存到 {summary_file}")

    def _build_session_messages(self, extract_dir):
        """从解压目录中读取所有消息文件并构建会话消息映射
        
        Args:
            extract_dir: 解压目录
            
        Returns:
            会话消息映射字典 {session_id: [messages]}
        """
        print(f"[DEBUG] 开始从目录 {extract_dir} 读取消息文件")
        session_messages = defaultdict(list)
        message_files = []
        
        # 寻找所有message.txt文件
        for root, dirs, files in os.walk(extract_dir):
            if 'message.txt' in files:
                message_files.append(os.path.join(root, 'message.txt'))
                
        print(f"[DEBUG] 找到 {len(message_files)} 个消息文件")
        
        for message_file in message_files:
            print(f"[DEBUG] 处理消息文件: {message_file}")
            try:
                with open(message_file, 'r', encoding='utf-8') as f:
                    line_count = 0
                    valid_messages = 0
                    for line in f:
                        line_count += 1
                        try:
                            if not line.strip().startswith('{'):
                                continue
                            message_data = json.loads(line.strip())
                            session_id = message_data.get("sessionId")
                            if not session_id:
                                continue
                                
                            session_messages[session_id].append(message_data)
                            valid_messages += 1
                            
                            if line_count % 1000 == 0:
                                print(f"[DEBUG] 已处理 {line_count} 行，有效消息数: {valid_messages}")
                                
                        except json.JSONDecodeError:
                            print(f"[DEBUG] 解析消息失败，行号: {line_count}")
                            continue
                        except Exception as e:
                            print(f"[DEBUG] 处理消息时出错，行号: {line_count}, 错误: {e}")
                            continue
                            
                print(f"[DEBUG] 文件 {message_file} 处理完成: 总行数={line_count}, 有效消息数={valid_messages}")
                
            except Exception as e:
                print(f"[ERROR] 读取消息文件 {message_file} 失败: {e}")
                
        print(f"[DEBUG] 消息文件处理完成，共处理 {len(session_messages)} 个会话的消息")
        return session_messages


def main():
    parser = argparse.ArgumentParser(description="处理七鱼会话数据并生成QA对")
    parser.add_argument("--start_date", type=str, required=True, help="开始日期，格式为YYYY-MM-DD HH:mm:ss")
    parser.add_argument("--end_date", type=str, required=True, help="结束日期，格式为YYYY-MM-DD HH:mm:ss")
    parser.add_argument("--output_dir", type=str, default="qa_results", help="输出目录")
    parser.add_argument("--threads", type=int, default=8, help="处理线程数")
    parser.add_argument("--test", action="store_true", help="测试模式，只处理少量数据")
    args = parser.parse_args()
    
    processor = QAProcessor(output_dir=args.output_dir, num_threads=args.threads)
    stats = processor.process_data(args.start_date, args.end_date, test_mode=args.test)
    
    if stats:
        print("\n--- 处理统计 ---")
        print(f"总会话数: {stats['total_sessions']}")
        print(f"成功处理数: {stats['success_count']}")
        print(f"处理失败数: {stats['fail_count']}")
        print(f"处理出错数: {stats['error_count']}")
        print("-----------------")
    

if __name__ == "__main__":
    main()