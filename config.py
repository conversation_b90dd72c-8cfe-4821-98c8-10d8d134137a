# config.py

# 七鱼 API 配置
# 警告: 请勿将敏感信息直接提交到版本控制系统。
# 建议使用环境变量或其他安全方式管理这些密钥。
QIYU_APPKEY = "6b647f0794b7065adc35551577f5939f" 
QIYU_ENCRYPT_KEY = "D4AE7FD636304DC7BDAD5DAB0CE3B319" 

# Prompt Service API 配置
PROMPT_SERVICE_ID = 139 # 测试
# PROMPT_SERVICE_ID = 140 # 线上
PROMPT_SERVICE_API_URL = "https://fastflow.longbridge-inc.com/apps/node/prompts_service"
# Prompt Service 读取超时时间（秒）
PROMPT_SERVICE_READ_TIMEOUT = 120
GRPC_TIME = 240

# 新增：并发处理配置
MAX_CONCURRENT_THREADS = 8 # 同时请求 Prompt Service 的最大线程数

# 飞书机器人告警配置
# 新增：飞书应用配置，用于卡片交互回调验证
FEISHU_APP_ID = "cli_a775d753b35c500c"  # 飞书应用 ID
FEISHU_APP_SECRET = "GMFEBBtskvqYzHlG4IOOlhP8QuLLTEO3"  # 飞书应用密钥，用于验证回调
# FEISHU_APP_ID = "cli_a89b4d3c423c500d"  # 线上飞书应用 ID
# FEISHU_APP_SECRET = "7vE34GJc1fv0u2m8lHpXQbVgrC3iCB5S"  # 线上飞书应用密钥，用于验证回调

# 飞书 chat_id
FEISHU_CHAT_ID = "oc_ee58c5b48a62006a4c529116950d1ad8" # test
# FEISHU_CHAT_ID = "oc_cd0845b63116ed844ffab2db9143fb49" # 正式群
# 飞书验证
FEISHU_ENCRYPT_KEY = ""
# FEISHU_VERIFICATION_TOKEN = "73gN62Kej192uStU91tKTbY3hUqJL4mj" #生产
FEISHU_VERIFICATION_TOKEN = "Qyn3HjzdsCTeTwKuCgXdDhGVQvtFj4sR" #测试
#飞书应用名称，用于@
# FEISHU_BOT_NAME = "客服会话告警机器人" # 生产
FEISHU_BOT_NAME = "chat-bot" # 测试

# 数据处理目录
BASE_DIR = "." # 替换为你的基础数据目录
DOWNLOAD_DIR = f"{BASE_DIR}/downloads"
EXTRACT_DIR = f"{BASE_DIR}/extracted_data"
# 新增：分析结果输出目录
ANALYSIS_OUTPUT_DIR = f"{BASE_DIR}/analysis_results"

# StarRocks 数据库配置
# STARROCKS_HOST = "fe-c-96436b24d25e4bfd-internal.starrocks.aliyuncs.com"  # 线上 StarRocks 数据库主机地址
# STARROCKS_HOST = "fe-c-0e33f105c10e0e51-internal.starrocks.aliyuncs.com"  # 测试地址
STARROCKS_HOST = "fe-c-0e33f105c10e0e51.starrocks.aliyuncs.com"  # 测试 debug 地址
STARROCKS_PORT = 9030  # StarRocks 数据库端口
# STARROCKS_USER = "ai_rw_tz8"  # StarRocks 数据库用户名
STARROCKS_USER = "admin"  # StarRocks 数据库用户名
# STARROCKS_PASSWORD = "VE#WUU&lBLk6m2"  # StarRocks 数据库密码
STARROCKS_PASSWORD = "xM#%A65Z#9EqPu"  # 测试 StarRocks 数据库密码
# STARROCKS_DATABASE = "ai_service"  # StarRocks 数据库名称
STARROCKS_DATABASE = "test"  # 测试 StarRocks 数据库名称

# 其他配置...
# 例如，轮询间隔、最大重试次数等
POLL_INTERVAL_SECONDS = 10
MAX_POLL_ATTEMPTS = 30

# 新增：任务检查器配置
CHECKER_INTERVAL = 60*5  # 检查器轮询间隔（秒），默认5分钟
TASK_REMINDER_THRESHOLD = 60*72  # 任务提醒阈值（分钟），超过此时间未处理的任务将发送提醒
CHECKER_DEFAULT_TIME = "10:00"  # 任务检查器默认执行时间（24小时制）
FEISHU_TASKLIST_GUID = "1d36dc9c-9212-4472-a3af-957cc4610b82" # 测试
# FEISHU_TASKLIST_GUID = "ed99ce39-3259-40c9-9d2f-2be4ddf5b35f" # 生产

# 飞书任务接收人 open_id 列表池 ['on_7f0941e5daac75b5716adb67241504c6','on_4a4f10801e713b71abe1bd2ad1c02aaf', 'on_6d5f307116ffbdf986c987b40036dc8d', 'on_1621c40ad7582cf45f6474f708606117', 'on_f3dd0f969823e584dc065bc0cf794e33']
# FEISHU_TASK_ASSIGNEES = ['on_2f71feecca2ada23c95c08ab3150664e', 'on_485309d9ab4c5791b854e1ad181fe10d', 'on_565c44713a6708e9ff9bd183df09e191']  # 替换为实际的接收人 union_id

"""
陆逊    ou_d032ed071bfafc11cc9470deb65a5b9f
慕辰    ou_c8d4519682685c4f8065b892af70ab04
沐风    ou_256d27d1f3101afffc17362bfffd56b7
study   ou_088519a555c015867bac296d89a55999
星河    ou_213a21f2e0b82c92d3489bf3e4d62592
飐竹    ou_1193a92ef8b2b5146b933c24295bd46e
Byron   ou_252d83b0bc85e07a75ef267f90b52f87
William ou_481dd3fe53c5cd8b064d9672f35b13c6     
"""
# Qdrant 向量数据库配置
QDRANT_URL = "https://c39b448c-d28b-44bd-8ef7-c06f67a04862.ap-southeast-1-0.aws.cloud.qdrant.io:6333"  # 替换为实际的Qdrant服务地址
QDRANT_API_KEY = "2yBINfTMmp0kYAVAJYXbIeMhq893DeTVUdeL8JnU1B6qySuQ3eJX4w"     # 替换为实际的Qdrant API Key

# QA 相似度配置
QA_SIMILARITY_THRESHOLD = 0.75  # 问题相似度阈值，超过此值视为同义问题
QA_MODEL_NAME = "shibing624/text2vec-base-chinese"  # 文本向量模型名称
