import subprocess
from datetime import datetime, timedelta

# 定义起始日期和结束日期
start_date = datetime.strptime("2025-01-01", "%Y-%m-%d")
end_date = datetime.strptime("2025-05-29", "%Y-%m-%d")

# 当前日期指针
current_date = start_date

# 循环生成并执行命令
def date_add(date_str, param):
    return (datetime.strptime(date_str, "%Y-%m-%d") + timedelta(days=param)).strftime("%Y-%m-%d")


while current_date < end_date:
    # 计算窗口结束日期
    window_end_date = current_date + timedelta(days=2)
    if window_end_date > end_date:
        window_end_date = end_date  # 确保不超过结束日期

    # 格式化日期为字符串
    start_date_str = current_date.strftime("%Y-%m-%d")
    end_date_str = window_end_date.strftime("%Y-%m-%d")

    # 构造命令
    command = f"python qa_normalize.py --qa_dir qa_results/{start_date_str}_to_{date_add(start_date_str,2)}/qa_pairs --concurrent --max_concurrent 20 --process_batch_size 200 --db_batch_size 1000 --threshold 0.8 --fallback_threshold 0.6"
    # command = f"python qa_processor.py --start_date \"{start_date_str}\" --end_date \"{end_date_str}\" --threads 24"
    print(f"Executing: {command}")

    # 执行命令
    subprocess.run(command, shell=True)

    # 更新当前日期指针
    current_date = window_end_date

